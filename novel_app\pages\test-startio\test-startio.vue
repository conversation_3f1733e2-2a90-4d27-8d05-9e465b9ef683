<template>
	<view class="test-container">
		<view class="header">
			<text class="title">Start.io广告插件测试</text>
		</view>

		<view class="test-section">
			<text class="section-title">SDK状态</text>
			<view class="status-info">
				<text class="status-label">初始化状态:</text>
				<text class="status-value" :class="{ success: sdkStatus.isInitialized, error: !sdkStatus.isInitialized }">
					{{ sdkStatus.isInitialized ? '已初始化' : '未初始化' }}
				</text>
			</view>
			<view class="status-info">
				<text class="status-label">App ID:</text>
				<text class="status-value">{{ sdkStatus.appId }}</text>
			</view>
			<view class="status-info">
				<text class="status-label">插件状态:</text>
				<text class="status-value" :class="{ success: sdkStatus.pluginAvailable, error: !sdkStatus.pluginAvailable }">
					{{ sdkStatus.pluginAvailable ? '插件可用' : '插件不可用' }}
				</text>
			</view>
		</view>

		<view class="test-section">
			<text class="section-title">功能测试</text>
			<view class="button-group">
				<button type="primary" @click="initializeSDK" :disabled="loading">
					{{ loading ? '初始化中...' : '初始化SDK' }}
				</button>
				<button type="default" @click="checkSDKStatus" :disabled="loading">检查SDK状态</button>
				<button type="warn" @click="showInterstitialAd" :disabled="loading">显示插屏广告</button>
			</view>
		</view>

		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y="true">
				<view class="log-item" v-for="(log, index) in logs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
			<view class="log-actions">
				<button size="mini" @click="clearLogs">清空日志</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: false,
			sdkStatus: {
				isInitialized: false,
				appId: '204660837',
				pluginAvailable: false
			},
			logs: []
		};
	},
	onLoad() {
		this.addLog('页面加载完成', 'info');
		this.checkSDKStatus();
	},
	methods: {
		addLog(message, type = 'info') {
			const now = new Date();
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
			this.logs.unshift({
				time,
				message,
				type
			});
			// 限制日志数量
			if (this.logs.length > 50) {
				this.logs = this.logs.slice(0, 50);
			}
		},

		clearLogs() {
			this.logs = [];
			this.addLog('日志已清空', 'info');
		},

		async initializeSDK() {
			this.loading = true;
			this.addLog('开始初始化Start.io SDK...', 'info');
			
			try {
				// 导入Start.io工具类
				const { StartIOAd } = await import('@/utils/startio.js');
				
				// 初始化SDK
				await StartIOAd.init();
				
				this.addLog('SDK初始化成功', 'success');
				this.sdkStatus.isInitialized = true;
				
				uni.showToast({
					title: 'SDK初始化成功',
					icon: 'success'
				});
			} catch (error) {
				this.addLog(`SDK初始化失败: ${error.message}`, 'error');
				console.error('SDK初始化失败:', error);
				
				uni.showToast({
					title: 'SDK初始化失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		async checkSDKStatus() {
			this.loading = true;
			this.addLog('检查SDK状态...', 'info');
			
			try {
				// 检查插件是否可用
				// #ifdef APP-PLUS
				const startIOModule = uni.requireNativePlugin('StartIOAd');
				this.sdkStatus.pluginAvailable = !!startIOModule;
				this.addLog(`原生插件状态: ${this.sdkStatus.pluginAvailable ? '可用' : '不可用'}`, this.sdkStatus.pluginAvailable ? 'success' : 'error');
				// #endif
				
				// #ifndef APP-PLUS
				this.sdkStatus.pluginAvailable = false;
				this.addLog('当前不在APP环境，插件不可用', 'warn');
				// #endif
				
				// 导入Start.io工具类
				const { StartIOAd } = await import('@/utils/startio.js');
				
				// 检查SDK状态
				const isReady = await StartIOAd.isReady();
				const appId = await StartIOAd.getAppId();
				
				this.sdkStatus.isInitialized = isReady;
				this.sdkStatus.appId = appId;
				
				this.addLog(`SDK状态: ${isReady ? '已初始化' : '未初始化'}`, isReady ? 'success' : 'warn');
				this.addLog(`App ID: ${appId}`, 'info');
				
			} catch (error) {
				this.addLog(`状态检查失败: ${error.message}`, 'error');
				console.error('状态检查失败:', error);
			} finally {
				this.loading = false;
			}
		},

		async showInterstitialAd() {
			this.loading = true;
			this.addLog('尝试显示插屏广告...', 'info');
			
			try {
				// 导入Start.io工具类
				const { StartIOAd } = await import('@/utils/startio.js');
				
				// 显示插屏广告
				await StartIOAd.safeShowInterstitial(
					{ scene: 'test_page' },
					(result) => {
						this.addLog('插屏广告显示成功', 'success');
						uni.showToast({
							title: '广告显示成功',
							icon: 'success'
						});
					},
					(error) => {
						this.addLog(`插屏广告显示失败: ${error.message}`, 'error');
						uni.showToast({
							title: '广告显示失败',
							icon: 'none'
						});
					}
				);
			} catch (error) {
				this.addLog(`插屏广告调用失败: ${error.message}`, 'error');
				console.error('插屏广告调用失败:', error);
				
				uni.showToast({
					title: '广告调用失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		}
	}
};
</script>

<style scoped>
.test-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background-color: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.status-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.status-label {
	font-size: 28rpx;
	color: #666;
}

.status-value {
	font-size: 28rpx;
	color: #333;
}

.status-value.success {
	color: #52c41a;
}

.status-value.error {
	color: #ff4d4f;
}

.button-group {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.log-container {
	height: 400rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	padding: 20rpx;
	background-color: #fafafa;
}

.log-item {
	margin-bottom: 10rpx;
	padding: 10rpx;
	background-color: white;
	border-radius: 4rpx;
	border-left: 4rpx solid #ddd;
}

.log-time {
	font-size: 24rpx;
	color: #999;
	margin-right: 20rpx;
}

.log-message {
	font-size: 26rpx;
	color: #333;
}

.log-message.success {
	color: #52c41a;
}

.log-message.error {
	color: #ff4d4f;
}

.log-message.warn {
	color: #faad14;
}

.log-message.info {
	color: #1890ff;
}

.log-actions {
	margin-top: 20rpx;
	text-align: center;
}
</style>
