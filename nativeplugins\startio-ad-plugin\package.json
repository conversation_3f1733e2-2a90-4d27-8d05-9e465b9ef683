{"name": "startio-ad-plugin", "id": "startio-ad-plugin", "version": "1.0.0", "description": "Start.io广告插件，为uni-app提供Banner和插屏广告功能", "main": "index.js", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "StartIOAd", "class": "io.dcloud.feature.startio.StartIOAdModule"}], "integrateType": "aar", "dependencies": ["com.startapp:inapp-sdk:5.1.0", "androidx.appcompat:appcompat:1.6.1", "androidx.core:core:1.10.1", "com.alibaba:<PERSON><PERSON><PERSON>:1.2.83"], "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.BLUETOOTH", "android.permission.AD_ID"], "minSdkVersion": "21", "parameters": {"appId": {"des": "Start.io应用ID", "type": "String", "required": true, "default": "204660837"}}}}, "keywords": ["startio", "advertisement", "banner", "interstitial", "uni-app", "plugin"], "author": {"name": "NovelBike Team", "email": "<EMAIL>"}, "license": "MIT", "engines": {"HBuilderX": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/gyyxs88/novel_app"}, "homepage": "https://novelbike.com", "bugs": {"url": "https://github.com/gyyxs88/novel_app/issues"}}