/**
 * English language pack
 */

export default {
  // Common text
  common: {
    loading: 'Loading...',
    retry: 'Retry',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    search: 'Search',
    clear: 'Clear',
    more: 'More',
    less: 'Less',
    expand: 'Expand',
    close: 'Close',
    open: 'Open',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info',
    unknown: 'Unknown',
    other: 'Other',
    all: 'All',
    none: 'None',
    empty: 'No data',
    noData: 'No data',
    noMore: 'No more data',
    noMoreData: 'No more data',
    loadMore: 'Load more',
    loadingMore: 'Loading more...',
    refresh: 'Refresh',
    refreshSuccess: 'Refresh successful',
    time: 'Time',
    minutesAgo: '{minutes} minutes ago',
    hoursAgo: '{hours} hours ago',
    daysAgo: '{days} days ago',
    yesterday: 'Yesterday',
    navigationFailed: 'Navigation failed',
    day: 'Day',
    letterSpacing: 'Letter Spacing',
    theme: 'Theme',
    chapter: ' chapters'
  },

  // Navigation titles
  nav: {
    bookstore: 'Bookstore',
    bookshelf: 'Bookshelf',
    profile: 'Profile',
    bookDetail: 'Book Details',
    reader: 'Reader',
    category: 'Category',
    search: 'Search',
    rankings: 'Rankings',
    login: 'Login',
    register: 'Register',
    readingHistory: 'Reading History',

    readingSettings: 'Reading Settings',
    themeSettings: 'Theme Settings',
    cacheSettings: 'Cache Management',
    about: 'About',
    feedback: 'Feedback',
    apiTest: 'API Test',
    readingDataDebug: 'Reading Data Debug',
    simpleDebug: 'Simple Debug'
  },

  // Tab bar
  tabBar: {
    bookstore: 'Store',
    bookshelf: 'Shelf',
    profile: 'Profile'
  },

  // Bookstore page
  bookstore: {
    searchPlaceholder: 'Search books, authors',
    rankings: 'Rankings',
    viewAll: 'View All',
    recommended: 'Recommended',
    recentUpdated: 'Recently Updated',
    trending: 'Trending',
    popular: 'Popular',
    new: 'New',
    completed: 'Completed',
    loadFailed: 'Load failed, please retry',
    loadMoreData: 'Load more data'
  },

  // Bookshelf page
  bookshelf: {
    collection: 'Collection',
    history: 'History',
    gridView: 'Grid View',
    listView: 'List View',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    deleteSelected: 'Delete Selected',
    confirmDelete: 'Confirm Delete',
    confirmDeleteContent: 'Are you sure to delete {count} selected books?',
    deleteSuccess: 'Delete successful',
    deleteFailed: 'Delete failed, please retry',
    deleting: 'Deleting...',
    selectBooksToDelete: 'Please select books to delete',
    emptyCollection: 'No books in your bookshelf, go to bookstore to explore',
    emptyHistory: 'No reading history',
    addToBookshelf: 'Go to bookstore to add your favorite books'
  },

  // Profile page
  profile: {
    clickToLogin: 'Tap to Login',
    readingDays: 'Reading Days',
    readingTime: 'Reading Time (min)',
    bookCount: 'Books Collected',
    readingHistory: 'Reading History',

    readingSettings: 'Reading Settings',
    themeSettings: 'Theme Settings',
    cacheManagement: 'Cache Management',
    languageSettings: 'Language Settings',
    aboutUs: 'About Us',
    feedback: 'Feedback',
    debugData: 'Reading Data Debug',
    logout: 'Logout',
    confirmLogout: 'Are you sure to logout?',
    logoutSuccess: 'Logged out successfully',
    loginRequired: 'Please login first',
    pageNavigationFailed: 'Page navigation failed'
  },

  // Login page
  login: {
    welcome: 'Welcome Back',
    emailOrUsername: 'Email or Username',
    emailPlaceholder: 'Enter email or username',
    password: 'Password',
    passwordPlaceholder: 'Enter password',
    loginButton: 'Login',
    loggingIn: 'Logging in...',
    noAccount: 'No account? Sign up now',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed, please check username and password',
    fillComplete: 'Please fill in complete login information'
  },

  // Register page
  register: {
    createAccount: 'Create Account',
    username: 'Username',
    usernamePlaceholder: 'Enter username',
    email: 'Email',
    emailPlaceholder: 'Enter email address',
    password: 'Password',
    passwordPlaceholder: 'Enter password (at least 8 characters, including letters and numbers)',
    confirmPassword: 'Confirm Password',
    confirmPasswordPlaceholder: 'Enter password again',
    registerButton: 'Register',
    registering: 'Registering...',
    hasAccount: 'Already have an account? Login now',
    registerSuccess: 'Registration successful, automatically logged in',
    registerFailed: 'Registration failed, please try again later',
    passwordMismatch: 'Passwords do not match',
    passwordInvalid: 'Password must be at least 8 characters long and contain at least one letter and one number',
    usernameInvalid: 'Username should be 3-20 characters long and contain only letters, numbers and underscores',
    emailInvalid: 'Please enter a valid email address',
    fillComplete: 'Please fill in complete and correct registration information',
    usernameExists: 'Username already exists, please choose another one',
    emailExists: 'Email already registered, please use another email'
  },

  // Book detail page
  bookDetail: {
    unknownTitle: 'Unknown Title',
    unknownAuthor: 'Unknown Author',
    category: 'Category',
    wordCount: 'Word Count',
    status: 'Status',
    rating: 'Rating',
    views: 'Views',
    rateThisBook: 'Rate this book:',
    startReading: 'Start Reading',
    continueReading: 'Continue Reading',
    addToBookshelf: 'Add to Bookshelf',
    collected: 'Collected',
    introduction: 'Introduction',
    catalog: 'Catalog',
    ascending: 'Ascending',
    descending: 'Descending',
    totalChapters: '{count} chapters total',
    newChapter: 'New',
    showMoreChapters: 'Show more chapters ({current}/{total})',
    similarRecommendations: 'Similar Books',
    ongoing: 'Ongoing',
    completed: 'Completed'
  },

  // Search page
  search: {
    searchPlaceholder: 'Search books, authors',
    searchHistory: 'Search History',
    hotSearch: 'Hot Search',
    clearHistory: 'Clear',
    confirmClearHistory: 'Are you sure to clear search history?',
    searching: 'Searching...',
    searchFailed: 'Search failed, please retry',
    foundBooks: 'Found {count} related books',
    noResults: 'No books found',
    tryOtherKeywords: 'Try other keywords',
    noDescription: 'No description'
  },

  // Reader page
  reader: {
    fontSize: 'Font Size',
    fontFamily: 'Font Family',
    lineHeight: 'Line Height',
    backgroundColor: 'Background Color',
    brightness: 'Brightness',
    pageMode: 'Page Mode',
    autoScroll: 'Auto Scroll',
    nightMode: 'Night Mode',
    settings: 'Settings',
    catalog: 'Catalog',
    bookmark: 'Bookmark',
    progress: 'Progress',
    chapter: 'Chapter {number}',
    previousChapter: 'Previous Chapter',
    nextChapter: 'Next Chapter',
    loadingChapter: 'Loading chapter...',
    chapterLoadFailed: 'Chapter load failed',
    endOfBook: 'End of book',
    beginningOfBook: 'Beginning of book',
    themeDefault: 'Default',
    themeEyeCare: 'Eye Care',
    themeDark: 'Dark',
    themeGreen: 'Green',
    fontDefault: 'Default',
    fontSerif: 'Serif',
    fontMono: 'Monospace',
    unknownChapter: 'Unknown Chapter'
  },

  // Category page
  category: {
    allCategories: 'All Categories',
    romance: 'Romance',
    fantasy: 'Fantasy',
    martial: 'Martial Arts',
    drama: 'Drama',
    horror: 'Horror',
    scifi: 'Sci-Fi',
    adventure: 'Adventure',
    mystery: 'Mystery',
    thriller: 'Thriller',
    comedy: 'Comedy',
    action: 'Action',
    historical: 'Historical',
    urban: 'Urban',
    schoolLife: 'School Life',
    sports: 'Sports',
    military: 'Military',
    business: 'Business',
    gaming: 'Gaming'
  },

  // Rankings page
  rankings: {
    newBooks: 'New Books',
    trending: 'Trending',
    popular: 'Popular',
    completed: 'Completed',
    noData: 'No ranking data available'
  },

  // Settings page
  settings: {
    language: 'Language',
    languageSettings: 'Language Settings',
    selectLanguage: 'Select Language',
    languageNote: 'Language settings will take effect on next app launch',
    switchingLanguage: 'Switching language...',
    languageSwitched: 'Language switched successfully',
    languageSwitchFailed: 'Language switch failed',
    theme: 'Theme',
    darkMode: 'Dark Mode',
    autoMode: 'Follow System',
    cache: 'Cache',
    clearCache: 'Clear Cache',
    cacheSize: 'Cache Size',
    about: 'About',
    version: 'Version',
    feedback: 'Feedback',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service'
  },

  // About page
  about: {
    emailCopied: 'Email copied',
    websiteCopied: 'Website URL copied',
    privacyPolicy: 'Privacy Policy',
    privacyPolicyContent: 'We value your privacy. Please visit our website for detailed privacy policy.',
    termsOfService: 'Terms of Service',
    termsOfServiceContent: 'By using this app, you agree to our terms of service. Please visit our website for detailed terms.',
    openSourceLicense: 'Open Source License',
    openSourceLicenseContent: 'This app uses multiple open source projects. Thanks to the open source community for their contributions.',
    // App info
    appName: 'NovelBike',
    version: 'Version 1.0.0',
    slogan: 'Make Reading Better',
    aboutApp: 'About NovelBike',
    appDescription1: 'NovelBike is a novel reading app focused on providing high-quality reading experience. We are committed to providing users with rich novel resources, smooth reading experience and personalized reading settings.',
    appDescription2: 'No matter what type of novels you like, you can find your favorite works here. We support multiple reading modes, so you can enjoy a comfortable reading experience in any environment.',
    // Features
    features: 'Features',
    richLibrary: 'Rich Library',
    richLibraryDesc: 'Massive novel resources, multiple categories',
    personalTheme: 'Personal Theme',
    personalThemeDesc: 'Multiple reading themes, eye protection mode',
    cloudSync: 'Cloud Sync',
    cloudSyncDesc: 'Reading progress cloud sync',
    readingStats: 'Reading Stats',
    readingStatsDesc: 'Detailed reading data analysis',
    // Team
    team: 'Development Team',
    teamName: 'NovelBike Team',
    teamDesc: 'A development team focused on reading experience',
    // Contact
    contact: 'Contact Us',
    email: 'Email',
    website: 'Website',
    // Legal
    legal: 'Legal Information'
  },

  // Reading History page
  readingHistory: {
    title: 'Reading History',
    all: 'All',
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    loading: 'Loading...',
    loadFailed: 'Load failed, please retry',
    retry: 'Retry',
    readingTo: 'Reading to:',
    continueReading: 'Continue',
    unknownAuthor: 'Unknown Author',
    emptyState: 'No reading history',
    emptyHint: 'Start reading a book',
    seconds: '{seconds}s',
    minutes: '{minutes}m',
    hours: '{hours}h',
    hoursMinutes: '{hours}h{minutes}m',
    minutesAgo: '{minutes} minutes ago',
    hoursAgo: '{hours} hours ago',
    daysAgo: '{days} days ago'
  },



  // Reading Settings page
  readingSettings: {
    title: 'Reading Settings',
    fontSettings: 'Font Settings',
    fontSize: 'Font Size',
    fontType: 'Font Type',
    lineHeight: 'Line Height',
    previewEffect: 'Preview Effect',
    previewText: 'This is a reading preview text. You can see the effect of current settings here. Font size, line height, background color and other settings will be displayed here in real time.',
    resetSettings: 'Reset to Default',
    confirmReset: 'Confirm Reset',
    confirmResetMessage: 'Are you sure to reset to default settings?',
    resetSuccess: 'Reset to default settings successfully',
    fontSaved: 'Font: {fontName}',
    fontSizeChanged: 'Font Size: {size}px',
    lineHeightChanged: 'Line Height: {height}',
    // Font options
    systemDefault: 'System Default',
    songFont: 'Song',
    heiFont: 'Hei',
    kaiFont: 'Kai'
  },

  // Cache Settings page
  cacheSettings: {
    title: 'Cache Management',
    cacheUsage: 'Cache Usage',
    imageCache: 'Image Cache',
    chapterCache: 'Chapter Cache',
    dataCache: 'Data Cache',
    totalCache: 'Total',
    cacheCleanup: 'Cache Cleanup',
    clearImageCache: 'Clear Image Cache',
    clearImageCacheDesc: 'Clear book covers and other image cache',
    clearChapterCache: 'Clear Chapter Cache',
    clearChapterCacheDesc: 'Clear cached chapter content',
    clearDataCache: 'Clear Data Cache',
    clearDataCacheDesc: 'Clear book lists, categories and other data cache',
    clearAllCache: 'Clear All Cache',
    clearAllCacheDesc: 'Clear all cache data (excluding user data)',
    clear: 'Clear',
    clearAll: 'Clear All',
    cacheSettingsTitle: 'Cache Settings',
    autoCleanCache: 'Auto Clean Cache',
    autoCleanCacheDesc: 'Automatically clean old cache when cache exceeds limit',
    cacheSizeLimit: 'Cache Size Limit',
    preloadChapters: 'Preload Chapters',
    preloadChaptersDesc: 'Automatically preload next chapter while reading',
    preloadCount: 'Preload Count',
    storageInfo: 'Storage Information',
    appData: 'App Data',
    userData: 'User Data',
    availableSpace: 'Available Space',
    confirmClear: 'Confirm Clear',
    confirmClearImage: 'Are you sure to clear image cache?',
    confirmClearChapter: 'Are you sure to clear chapter cache? You will need to reload chapter content after clearing.',
    confirmClearData: 'Are you sure to clear data cache?',
    confirmClearAll: 'Are you sure to clear all cache? This will clear all cache data (excluding user data and settings).',
    clearing: 'Clearing...',
    imageCacheCleared: 'Image cache cleared',
    chapterCacheCleared: 'Chapter cache cleared',
    dataCacheCleared: 'Data cache cleared',
    allCacheCleared: 'All cache cleared'
  },

  // Feedback page
  feedback: {
    title: 'Feedback',
    feedbackType: 'Feedback Type',
    problemDescription: 'Problem Description *',
    contactInfo: 'Contact Information',
    deviceInfo: 'Device Information',
    submitFeedback: 'Submit Feedback',
    submitting: 'Submitting...',
    loadingCategories: 'Loading categories...',
    descriptionPlaceholder: 'Please describe the problem or suggestion in detail...',
    contactPlaceholder: 'Please leave your email or phone number (optional)',
    contactHint: 'Leave your contact information, we will reply to you in time',
    deviceInfoHint: 'Including device information helps us solve problems better',
    deviceModel: 'Device Model:',
    systemVersion: 'System Version:',
    appVersion: 'App Version:',
    submitSuccess: 'Feedback submitted successfully',
    submitSuccessWithTicket: 'Feedback submitted successfully, ticket number: {ticketNumber}',
    submitFailed: 'Submission failed, please try again',
    // FAQ
    faq: 'FAQ',
    faqSyncProgress: 'How to sync reading progress?',
    faqSyncProgressAnswer: 'After logging in, reading progress will be automatically synced to the cloud. Log in with the same account on other devices to sync progress.',
    faqCannotOpen: 'Why can\'t some books be opened?',
    faqCannotOpenAnswer: 'It may be a network issue or the book resource is temporarily unavailable. Please check your network connection or try again later.',
    faqChangeFont: 'How to change reading font?',
    faqChangeFontAnswer: 'Click the settings button on the reading page, or adjust font size and type in "Profile" - "Reading Settings".',
    faqClearCache: 'How to clear cache?',
    faqClearCacheAnswer: 'You can clear various cache data in "Profile" - "Cache Management" to free up storage space.',
    // Default feedback types
    bugReport: 'Bug Report',
    featureSuggestion: 'Feature Suggestion',
    usageIssue: 'Usage Issue',
    other: 'Other'
  },

  // Update dialog
  update: {
    newVersionAvailable: 'New Version Available',
    currentVersion: 'Current Version',
    latestVersion: 'Latest Version',
    updateContent: 'Update Content',
    bugFixesAndImprovements: 'Bug fixes and performance improvements',
    downloading: 'Downloading',
    later: 'Later',
    updateNow: 'Update Now',
    cancel: 'Cancel',
    continue: 'Continue',
    mobileNetworkTip: 'You are using mobile network, downloading may consume data',
    networkConfirm: 'Network Reminder',
    mobileNetworkConfirmContent: 'You are using mobile network. Continue downloading?',
    storagePermissionRequired: 'Storage permission required',
    updateFailed: 'Update failed',
    checkUpdate: 'Check Update',
    checkingUpdate: 'Checking for updates...',
    noUpdateAvailable: 'You are using the latest version',
    updateAvailable: 'New version available',
    forceUpdateTitle: 'Update Required',
    forceUpdateContent: 'This version is no longer supported, please update to continue using',
    downloadCompleted: 'Download completed, installing...',
    installFailed: 'Installation failed, please install manually'
  },

  // Error messages
  error: {
    networkError: 'Network connection failed',
    serverError: 'Server error',
    notFound: 'Page not found',
    unauthorized: 'Unauthorized access',
    forbidden: 'Access denied',
    timeout: 'Request timeout',
    unknown: 'Unknown error'
  },


};
