<template>
	<view class="bookstore-container">
		<!-- 下拉刷新区域 -->
		<scroll-view
			scroll-y
			class="bookstore-scroll"
			@scrolltolower="loadMore"
			@refresherrefresh="onRefresh"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
		>
		<!-- 搜索栏 -->
		<view class="search-bar" @click="navigateToSearch">
			<text class="search-icon">🔍</text>
			<text class="search-input">{{ $t('bookstore.searchPlaceholder') }}</text>
		</view>



		<!-- 分类导航 -->
		<view class="categories-container">
			<!-- 使用uni-app的scroll-view组件，设置为横向滚动 -->
			<scroll-view
				scroll-x="true"
				class="categories"
				show-scrollbar="false"
				style="width: 100%; white-space: nowrap; overflow-x: auto;"
				:enhanced="true"
				:show-scrollbar="false"
			>
				<view class="categories-wrapper" style="display: flex; flex-direction: row; flex-wrap: nowrap;">
					<!-- 使用inline-block确保横向排列 -->
					<view
						class="category-item"
						v-for="(category, index) in displayCategories"
						:key="index"
						@click="navigateToCategory(category)"
					>
						<view class="category-icon">
							<image
								v-if="category.icon && category.icon.trim() && category.icon.startsWith('http')"
								:src="category.icon"
								mode="aspectFill"
								class="category-image"
							></image>
							<text v-else class="category-fa-icon">{{ getCategoryIconUnicode(category.name) }}</text>
						</view>
						<text class="category-name">{{ category.name }}</text>
					</view>

					<!-- 更多按钮 -->
					<view
						v-if="categories.length > 10"
						class="category-item"
						@click="navigateToAllCategories"
					>
						<view class="category-icon more-icon">
							<text class="category-fa-icon">⋯</text>
						</view>
						<text class="category-name">{{ $t('common.more') }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 排行榜 -->
		<view class="section-header">
			<text class="section-title">{{ $t('bookstore.rankings') }}</text>
			<text class="view-more" @click="navigateToRankings('new')">{{ $t('bookstore.viewAll') }}</text>
		</view>

		<view class="rankings-tabs">
			<view
				class="ranking-tab"
				v-for="(tab, index) in rankingTabs"
				:key="index"
				:class="{ active: currentRankingTab === tab.id }"
				@click="switchRankingTab(tab.id)"
			>
				<text class="tab-text">{{ tab.name }}</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading.rankings">
			<view class="loading-spinner"></view>
			<text class="loading-text">{{ $t('common.loading') }}</text>
		</view>

		<!-- 错误提示 -->
		<view class="error-container" v-else-if="error.rankings">
			<text class="error-text">{{ error.rankings.message || $t('bookstore.loadFailed') }}</text>
			<view class="error-retry" @click="loadRankingBooks">{{ $t('common.retry') }}</view>
		</view>

		<!-- 排行榜内容 -->
		<view class="ranking-list-container" v-else>
			<view class="ranking-list">
				<view class="ranking-column">
					<view class="ranking-item" v-for="(book, index) in rankingBooks.slice(0, 3)" :key="index" @click="navigateToBook(book.id)">
						<text class="ranking-number">{{ index + 1 }}</text>
						<image class="ranking-book-cover" :src="book.coverUrl" mode="aspectFill"></image>
						<view class="ranking-book-info">
							<text class="ranking-book-title">{{ book.title }}</text>
							<text class="ranking-book-author">{{ book.author }}</text>
							<view class="ranking-book-stats">
								<text class="stats-item">⭐ {{ book.rating }}</text>
								<text class="stats-item">👁️ {{ book.views }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="ranking-column">
					<view class="ranking-item" v-for="(book, index) in rankingBooks.slice(3, 6)" :key="index" @click="navigateToBook(book.id)">
						<text class="ranking-number">{{ index + 4 }}</text>
						<image class="ranking-book-cover" :src="book.coverUrl" mode="aspectFill"></image>
						<view class="ranking-book-info">
							<text class="ranking-book-title">{{ book.title }}</text>
							<text class="ranking-book-author">{{ book.author }}</text>
							<view class="ranking-book-stats">
								<text class="stats-item">⭐ {{ book.rating }}</text>
								<text class="stats-item">👁️ {{ book.views }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="ranking-column">
					<view class="ranking-item" v-for="(book, index) in rankingBooks.slice(6, 9)" :key="index" @click="navigateToBook(book.id)">
						<text class="ranking-number">{{ index + 7 }}</text>
						<image class="ranking-book-cover" :src="book.coverUrl" mode="aspectFill"></image>
						<view class="ranking-book-info">
							<text class="ranking-book-title">{{ book.title }}</text>
							<text class="ranking-book-author">{{ book.author }}</text>
							<view class="ranking-book-stats">
								<text class="stats-item">⭐ {{ book.rating }}</text>
								<text class="stats-item">👁️ {{ book.views }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 推荐书籍 -->
		<view class="recommended-section">
			<view class="section-header">
				<text class="section-title">{{ $t('bookstore.recommended') }}</text>
				<text class="view-more" @click="navigateToMore('recommended')">{{ $t('bookstore.viewAll') }}</text>
			</view>

			<!-- 加载状态 -->
			<view class="loading-container" v-if="loading.recommended">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ $t('common.loading') }}</text>
			</view>

			<!-- 错误提示 -->
			<view class="error-container" v-else-if="error.recommended">
				<text class="error-text">{{ error.recommended.message || $t('bookstore.loadFailed') }}</text>
				<view class="error-retry" @click="loadRecommendedBooks">{{ $t('common.retry') }}</view>
			</view>

			<!-- 推荐书籍内容 -->
			<view class="recommended-books-container" v-else>
				<scroll-view
					scroll-x="true"
					class="horizontal-scroll"
					show-scrollbar="false"
					:enhanced="true"
					:show-scrollbar="false"
				>
					<view class="books-wrapper" style="display: flex; flex-direction: row; flex-wrap: nowrap;">
						<view class="book-card" v-for="(book, index) in recommendedBooks" :key="index" @click="navigateToBook(book.id)">
							<image class="book-cover" :src="book.coverUrl" mode="aspectFill"></image>
							<text class="book-title">{{ book.title }}</text>
							<text class="book-author">{{ book.author }}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 最近更新 -->
		<view class="recent-updated-section">
			<view class="section-header">
				<text class="section-title">{{ $t('bookstore.recentUpdated') }}</text>
			</view>

			<!-- 加载状态 -->
			<view class="loading-container" v-if="loading.recentUpdated && recentUpdatedBooks.length === 0">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ $t('common.loading') }}</text>
			</view>

			<!-- 错误提示 -->
			<view class="error-container" v-else-if="error.recentUpdated && recentUpdatedBooks.length === 0">
				<text class="error-text">{{ error.recentUpdated.message || $t('bookstore.loadFailed') }}</text>
				<view class="error-retry" @click="loadRecentUpdatedBooks(true)">{{ $t('common.retry') }}</view>
			</view>

			<!-- 最近更新书籍列表 -->
			<view class="recent-updated-list" v-else>
				<view
					class="recent-book-item"
					v-for="(book, index) in recentUpdatedBooks"
					:key="index"
					@click="navigateToBook(book.id)"
				>
					<image class="recent-book-cover" :src="book.coverUrl" mode="aspectFill"></image>
					<view class="recent-book-info">
						<text class="recent-book-title">{{ book.title }}</text>
						<text class="recent-book-author">{{ book.author }}</text>
						<view class="recent-book-meta">
							<text class="recent-book-category">{{ book.category }}</text>
							<text class="recent-book-updated">{{ formatUpdateTime(book.updatedAt) }}</text>
						</view>
						<view class="recent-book-stats">
							<text class="stats-item">⭐ {{ book.rating }}</text>
							<text class="stats-item">👁️ {{ book.views }}</text>
						</view>
					</view>
				</view>

				<!-- 加载更多状态 -->
				<view class="load-more-container" v-if="recentUpdatedHasMore">
					<view class="loading-container" v-if="loading.recentUpdated">
						<view class="loading-spinner"></view>
						<text class="loading-text">{{ $t('common.loadingMore') }}</text>
					</view>
					<view class="load-more-trigger" v-else @click="loadMoreRecentUpdated">
						<text class="load-more-text">{{ $t('common.loadMore') }}</text>
					</view>
				</view>

				<!-- 没有更多数据提示 -->
				<view class="no-more-container" v-else-if="recentUpdatedBooks.length > 0">
					<text class="no-more-text">{{ $t('common.noMoreData') }}</text>
				</view>
			</view>
		</view>
		</scroll-view>


	</view>
</template>

<script>
import { i18nMixin } from '@/utils/i18n.js';
import { autoTitleMixin } from '@/config/page-titles.js';

export default {
	mixins: [i18nMixin, autoTitleMixin],
		data() {
			return {
				categories: [],
				displayCategories: [], // 显示的分类（前10个）
				rankingTabsData: [
					{ id: 'trending', nameKey: 'bookstore.trending' },
					{ id: 'popular', nameKey: 'bookstore.popular' },
					{ id: 'new', nameKey: 'bookstore.new' }
				],
				currentRankingTab: 'trending',
				rankingBooks: [],
				recommendedBooks: [],
				recentUpdatedBooks: [],
				recentUpdatedPage: 1,
				recentUpdatedHasMore: true,
				loading: {
					rankings: false,
					recommended: false,
					recentUpdated: false
				},
				error: {
					rankings: null,
					recommended: null,
					recentUpdated: null
				},
			refreshing: false,
			hasMore: true,
			// 分类图标映射表
			categoryIconMap: {
				'Romance': 'fa-heart',
				'Fantasy': 'fa-dragon',
				'Martial Arts': 'fa-user-ninja',
				'Drama': 'fa-theater-masks',
				'Horror': 'fa-ghost',
				'Sci-fi': 'fa-rocket',
				'Adventure': 'fa-compass',
				'Mystery': 'fa-search',
				'Thriller': 'fa-bolt',
				'Comedy': 'fa-laugh',
				'Action': 'fa-fist-raised',
				'Adult': 'fa-user-secret',
				'Apocalypse': 'fa-skull',
				'Historical': 'fa-landmark',
				'Urban': 'fa-city',
				'School Life': 'fa-graduation-cap',
				'Sports': 'fa-running',
				'Military': 'fa-shield-alt',
				'Business': 'fa-briefcase',
				'Gaming': 'fa-gamepad',
				'Josei': 'fa-female',
				'Slice of Life': 'fa-coffee',
				'Supernatural': 'fa-magic',
				'Psychological': 'fa-brain',
				'Seinen': 'fa-male',
				'Shoujo': 'fa-star',
				'Shounen': 'fa-fire',
				'Wuxia': 'fa-sword',
				'Xianxia': 'fa-yin-yang',
				'Xuanhuan': 'fa-gem',
				'Ecchi': 'fa-kiss',
				'Harem': 'fa-users',
				'Mature': 'fa-exclamation-triangle',
				'Mecha': 'fa-robot',
				'Rebirth': 'fa-redo',
				'Smut': 'fa-fire-alt',
				'Tragedy': 'fa-sad-tear',
				'Yaoi': 'fa-mars-double',
				'Yuri': 'fa-venus-double',
				'Gender Bender': 'fa-transgender',
				'Shoujo Ai': 'fa-heart-broken',
				'Shounen Ai': 'fa-heart'
			}
			}
		},
		computed: {
			// 国际化的排行榜标签
			rankingTabs() {
				return this.rankingTabsData.map(tab => ({
					id: tab.id,
					name: this.$t(tab.nameKey)
				}));
			}
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			loadData() {
				this.loadCategories();
				this.loadRankingBooks();
				this.loadRecommendedBooks();
				this.loadRecentUpdatedBooks();
			},

			// 加载分类数据
			loadCategories() {
				this.$api.book.getCategories()
					.then(result => {
						console.log('分类数据:', result);
						if (result && result.categories) {
							// 按书籍数量排序（从多到少）
							const sortedCategories = result.categories.sort((a, b) => {
								const countA = a.book_count || 0;
								const countB = b.book_count || 0;
								return countB - countA;
							});

							this.categories = sortedCategories;
							// 只显示前10个分类
							this.displayCategories = sortedCategories.slice(0, 10);

							console.log('Display categories:', this.displayCategories.map(c => ({
								name: c.name,
								icon: c.icon,
								hasIcon: !!(c.icon && c.icon.trim() && c.icon.startsWith('http'))
							})));
						}
					})
					.catch(error => {
						console.error('Failed to load categories:', error);
						this.categories = [];
						this.displayCategories = [];
					});
			},
		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			// 重新加载数据
			this.loadData();
			// 模拟网络请求延迟
			setTimeout(() => {
				this.refreshing = false;
				uni.showToast({
					title: this.$t('common.refreshSuccess'),
					icon: 'success',
					duration: 1000
				});
			}, 1000);
		},
		// 加载更多
		loadMore() {
			// 优先加载最近更新的书籍
			if (this.recentUpdatedHasMore && !this.loading.recentUpdated) {
				this.loadMoreRecentUpdated();
				return;
			}

			if (!this.hasMore || this.loading.rankings || this.loading.recommended) return;

			// 这里可以实现加载更多的逻辑
			console.log(this.$t('bookstore.loadMoreData'));
		},
			// 加载排行榜书籍
			loadRankingBooks() {
				this.loading.rankings = true;
				this.error.rankings = null;

				this.$api.book.getRankings(this.currentRankingTab, { limit: 9 })
					.then(result => {
						console.log('Rankings data:', result);
						if (result && result.books) {
							this.rankingBooks = result.books.map((book, index) => {
								console.log(`Ranking book ${index + 1}:`, {
									originalId: book.id,
									idType: typeof book.id,
									title: book.title,
									rating: book.rating
								});
								return {
									id: book.id,
									title: book.title,
									author: book.author,
									coverUrl: book.cover_url || `https://via.placeholder.com/150x225?text=${encodeURIComponent(book.title)}`,
									rating: book.rating || 0,
									views: this.formatViews(book.views),
									rank: book.rank || (index + 1)
								};
							});
							console.log('Processed ranking books:', this.rankingBooks);
						}
					})
					.catch(error => {
						console.error('Failed to load rankings:', error);
						this.error.rankings = error;
						this.rankingBooks = [];
					})
					.finally(() => {
						this.loading.rankings = false;
					});
			},
			// 加载推荐书籍
			loadRecommendedBooks() {
				this.loading.recommended = true;
				this.error.recommended = null;

				this.$api.book.getRecommendations('trending', { limit: 10 })
					.then(result => {
						console.log('Recommended books data:', result);
						if (result && result.books) {
							this.recommendedBooks = result.books.map(book => ({
								id: book.id,
								title: book.title,
								author: book.author,
								coverUrl: book.cover_url || `https://via.placeholder.com/150x225?text=${encodeURIComponent(book.title)}`,
								rating: book.rating,
								views: this.formatViews(book.views)
							}));
						}
					})
					.catch(error => {
						console.error('Failed to load recommended books:', error);
						this.error.recommended = error;
						this.recommendedBooks = [];
					})
					.finally(() => {
						this.loading.recommended = false;
					});


			},

			// 加载最近更新书籍
			loadRecentUpdatedBooks(reset = false) {
				if (reset) {
					this.recentUpdatedPage = 1;
					this.recentUpdatedBooks = [];
					this.recentUpdatedHasMore = true;
				}

				this.loading.recentUpdated = true;
				this.error.recentUpdated = null;

				this.$api.book.getRecentUpdatedBooks({
					page: this.recentUpdatedPage,
					size: 10
				})
					.then(result => {
						console.log('Recent updated books data:', result);
						if (result && result.books) {
							const newBooks = result.books.map(book => ({
								id: book.id,
								title: book.title,
								author: book.author,
								coverUrl: book.cover_url || `https://via.placeholder.com/150x225?text=${encodeURIComponent(book.title)}`,
								rating: book.rating || 0,
								views: this.formatViews(book.views),
								category: book.category?.name || book.category || this.$t('common.unknown'),
								updatedAt: book.updated_at
							}));

							if (reset) {
								this.recentUpdatedBooks = newBooks;
							} else {
								this.recentUpdatedBooks = [...this.recentUpdatedBooks, ...newBooks];
							}

							// 检查是否还有更多数据
							this.recentUpdatedHasMore = newBooks.length === 10;
							if (this.recentUpdatedHasMore) {
								this.recentUpdatedPage++;
							}
						}
					})
					.catch(error => {
						console.error('Failed to load recent updated books:', error);
						this.error.recentUpdated = error;
					})
					.finally(() => {
						this.loading.recentUpdated = false;
					});
			},

			// 加载更多最近更新书籍
			loadMoreRecentUpdated() {
				if (!this.recentUpdatedHasMore || this.loading.recentUpdated) {
					return;
				}
				this.loadRecentUpdatedBooks(false);
			},

			// 格式化更新时间 - 使用国际化
			formatUpdateTime(updatedAt) {
				if (!updatedAt) return '';

				const now = new Date();
				const updateTime = new Date(updatedAt);
				const diffMs = now - updateTime;
				const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
				const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
				const diffMinutes = Math.floor(diffMs / (1000 * 60));

				if (diffMinutes < 60) {
					return this.$t('common.minutesAgo', { minutes: diffMinutes });
				} else if (diffHours < 24) {
					return this.$t('common.hoursAgo', { hours: diffHours });
				} else if (diffDays < 7) {
					return this.$t('common.daysAgo', { days: diffDays });
				} else {
					return updateTime.toLocaleDateString();
				}
			},

			// 格式化浏览量
			formatViews(views) {
				if (!views) return '0';
				if (views >= 1000000) {
					return (views / 1000000).toFixed(1) + 'M';
				} else if (views >= 1000) {
					return (views / 1000).toFixed(1) + 'K';
				}
				return views.toString();
			},

			switchRankingTab(tabId) {
				this.currentRankingTab = tabId;
				// 加载不同排行榜数据
				this.loadRankingBooks();
			},
			navigateToSearch() {
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},
			navigateToCategory(category) {
				uni.navigateTo({
					url: `/pages/category/category?id=${category.id}&name=${category.name}`
				});
			},
			navigateToRankings(rankingId) {
				let url = '/pages/rankings/rankings';
				if (rankingId) {
					url += `?id=${rankingId}`;
				}
				uni.navigateTo({
					url
				});
			},
			navigateToBook(bookId) {
				console.log('=== Navigate to book detail ===');
				console.log('Book ID:', bookId);
				console.log('ID type:', typeof bookId);
				console.log('Navigation URL:', `/pages/book-detail/book-detail?id=${bookId}`);

				uni.navigateTo({
					url: `/pages/book-detail/book-detail?id=${bookId}`
				});
			},
			navigateToMore(type) {
				let name = '';
				switch (type) {
					case 'recommended':
						name = this.$t('bookstore.recommended');
						break;
					case 'new':
						name = this.$t('bookstore.new');
						break;
					case 'completed':
						name = this.$t('bookstore.completed');
						break;
				}
				uni.navigateTo({
					url: `/pages/category/category?type=${type}&name=${name}`
				});
			},

			// 获取分类图标类名
			getCategoryIconClass(categoryName) {
				const iconClass = this.categoryIconMap[categoryName] || 'fa-book';
				console.log(`Category "${categoryName}" icon class: fa ${iconClass}`);
				return `fa ${iconClass}`;
			},

			// 获取分类图标Unicode字符（白色符号）
			getCategoryIconUnicode(categoryName) {
				const iconMap = {
					'Romance': '♡',
					'Fantasy': '✦',
					'Martial Arts': '⚔',
					'Drama': '♪',
					'Horror': '☠',
					'Sci-fi': '★',
					'Adventure': '⚡',
					'Mystery': '?',
					'Comedy': '◉',
					'Action': '⚡',
					'Adult': '♠',
					'Apocalypse': '☠',
					'Historical': '♛',
					'Urban': '▲',
					'School Life': '♦',
					'Sports': '♦',
					'Josei': '♀',
					'Slice of Life': '◇',
					'Supernatural': '✦',
					'Psychological': '◉',
					'Seinen': '♂',
					'Shoujo': '✧',
					'Shounen': '⚡',
					'Wuxia': '⚔',
					'Xianxia': '☯',
					'Xuanhuan': '◆',
					'Ecchi': '♡',
					'Harem': '♠',
					'Mature': '!',
					'Mecha': '⚙',
					'Rebirth': '↻',
					'Smut': '♨',
					'Tragedy': '♧',
					'Yaoi': '♂',
					'Yuri': '♀',
					'Gender Bender': '⚧',
					'Shoujo Ai': '♡',
					'Shounen Ai': '♡'
				};
				return iconMap[categoryName] || '◆';
			},

			// 导航到所有分类页面
			navigateToAllCategories() {
				const allCategoriesName = this.$t('category.allCategories');
				uni.navigateTo({
					url: `/pages/category/category?showAll=true&name=${encodeURIComponent(allCategoriesName)}`
				});
			},


		}
	}
</script>

<style>
	/* 基础样式 */
	:root {
		--primary-color: #1E5F74;
		--secondary-color: #133B5C;
		--accent-color: #A2A8D3;
		--background-color: #F8F9FA;
		--card-background: #FFFFFF;
		--text-primary: #212529;
		--text-secondary: #6C757D;
		--border-color: #DEE2E6;
	}

	.bookstore-container {
		background-color: var(--background-color);
		min-height: 100vh;
	}

	.bookstore-scroll {
		height: 100vh;
	}



	/* 搜索栏 */
	.search-bar {
		display: flex;
		align-items: center;
		background-color: rgba(142, 142, 147, 0.12);
		border-radius: 10px;
		padding: 0 16px;
		margin: 16px;
		height: 36px;
	}

	.search-icon {
		color: var(--text-secondary);
		margin-right: 8px;
	}

	.search-input {
		flex: 1;
		font-size: 17px;
		color: var(--text-secondary);
	}

	/* 分类导航 */
	.categories-container {
		width: 100%;
		overflow-x: hidden;
		margin-bottom: 16px;
	}

	.categories {
		width: 100%;
		overflow-x: auto;
		padding: 16px 0;
		white-space: nowrap;
		scrollbar-width: none;
	}

	.categories::-webkit-scrollbar {
		display: none;
	}

	.categories-wrapper {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		padding: 0 16px;
	}

	.category-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		margin-right: 24px;
		width: 70px;
		flex-shrink: 0;
	}

	.category-icon {
		width: 50px;
		height: 50px;
		border-radius: 25px;
		background-color: var(--primary-color);
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		margin-bottom: 8px;
		overflow: hidden;
		position: relative;
	}

	.category-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.fa {
		font-size: 20px;
	}

	.category-fa-icon {
		font-size: 22px;
		color: white;
		font-weight: bold;
		text-align: center;
		line-height: 1;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.more-icon {
		background-color: var(--text-secondary) !important;
	}

	.category-name {
		font-size: 12px;
		text-align: center;
		color: var(--text-primary);
		line-height: 1.2;
		width: 100%;
		word-wrap: break-word;
		overflow-wrap: break-word;
		hyphens: auto;
	}

	/* 章节标题 */
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 16px;
		margin: 16px 0 8px;
	}

	.section-title {
		font-size: 20px;
		font-weight: 600;
		margin: 0;
	}

	.view-more {
		font-size: 14px;
		color: var(--primary-color);
		text-decoration: none;
	}

	/* 排行榜标签 */
	.rankings-tabs {
		display: flex;
		border-bottom: 1px solid var(--border-color);
		margin-bottom: 16px;
		padding: 0 16px;
		overflow-x: auto;
		white-space: nowrap;
		scrollbar-width: none;
	}

	.rankings-tabs::-webkit-scrollbar {
		display: none;
	}

	.ranking-tab {
		padding: 8px 16px;
		font-size: 14px;
		font-weight: 500;
		color: var(--text-secondary);
		border-bottom: 2px solid transparent;
		margin-right: 16px;
	}

	.ranking-tab.active {
		color: var(--primary-color);
		border-bottom-color: var(--primary-color);
	}

	/* 排行榜列表 */
	.ranking-list-container {
		overflow-x: auto;
		scrollbar-width: none;
		padding: 0 16px;
	}

	.ranking-list-container::-webkit-scrollbar {
		display: none;
	}

	.ranking-list {
		display: flex;
		min-width: 150%;
	}

	.ranking-column {
		flex: 1;
		min-width: 50%;
	}

	.ranking-column:first-child {
		margin-right: 16px;
	}

	.ranking-item {
		display: flex;
		align-items: center;
		padding: 8px 0;
		border-bottom: 1px solid var(--border-color);
	}

	.ranking-item:last-child {
		border-bottom: none;
	}

	.ranking-number {
		font-size: 18px;
		font-weight: 700;
		color: var(--primary-color);
		width: 24px;
		text-align: center;
	}

	.ranking-book-cover {
		width: 50px;
		height: 75px;
		border-radius: 4px;
		object-fit: cover;
		margin: 0 8px;
	}

	.ranking-book-info {
		flex: 1;
		overflow: hidden;
	}

	.ranking-book-title {
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 2px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.ranking-book-author {
		font-size: 12px;
		color: var(--text-secondary);
		margin-bottom: 2px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.ranking-book-stats {
		font-size: 11px;
		color: var(--text-secondary);
		display: flex;
		align-items: center;
	}

	.stats-item {
		margin-right: 8px;
	}

	/* 推荐书籍 */
	.recommended-section {
		margin-top: 24px;
	}

	/* 最近更新 */
	.recent-updated-section {
		margin-top: 24px;
		padding-bottom: 20px;
	}

	.recommended-books-container {
		width: 100%;
		overflow-x: hidden;
	}

	.horizontal-scroll {
		width: 100%;
		overflow-x: auto;
		padding: 16px 0;
		scrollbar-width: none;
		white-space: nowrap;
	}

	.horizontal-scroll::-webkit-scrollbar {
		display: none;
	}

	.books-wrapper {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		padding: 0 16px;
	}

	.book-card {
		display: flex;
		flex-direction: column;
		width: 110px;
		margin-right: 16px;
		flex-shrink: 0;
	}

	.book-cover {
		width: 100%;
		height: 160px;
		border-radius: 4px;
		object-fit: cover;
		box-shadow: 0 2px 4px rgba(0,0,0,0.15);
		margin-bottom: 8px;
		background-color: #e0e0e0;
	}

	.book-title {
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 2px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.book-author {
		font-size: 12px;
		color: var(--text-secondary);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 加载状态 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.loading-spinner {
		width: 30px;
		height: 30px;
		border: 3px solid #f3f3f3;
		border-top: 3px solid #1E5F74;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 10px;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		color: #6C757D;
		font-size: 14px;
	}

	/* 错误提示 */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.error-text {
		color: #dc3545;
		font-size: 14px;
		margin-bottom: 10px;
		text-align: center;
	}

	.error-retry {
		color: #1E5F74;
		font-size: 14px;
		padding: 5px 15px;
		border: 1px solid #1E5F74;
		border-radius: 15px;
	}

	/* 最近更新书籍列表样式 */
	.recent-updated-list {
		padding: 0 16px;
	}

	.recent-book-item {
		display: flex;
		align-items: flex-start;
		padding: 12px 0;
		border-bottom: 1px solid var(--border-color);
	}

	.recent-book-item:last-child {
		border-bottom: none;
	}

	.recent-book-cover {
		width: 80px;
		height: 120px;
		border-radius: 6px;
		object-fit: cover;
		margin-right: 12px;
		flex-shrink: 0;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	}

	.recent-book-info {
		flex: 1;
		overflow: hidden;
	}

	.recent-book-title {
		font-size: 16px;
		font-weight: 600;
		color: var(--text-primary);
		margin-bottom: 4px;
		line-height: 1.3;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.recent-book-author {
		font-size: 14px;
		color: var(--text-secondary);
		margin-bottom: 6px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.recent-book-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 6px;
	}

	.recent-book-category {
		font-size: 12px;
		color: var(--primary-color);
		background-color: rgba(30, 95, 116, 0.1);
		padding: 2px 6px;
		border-radius: 10px;
	}

	.recent-book-updated {
		font-size: 12px;
		color: var(--text-secondary);
	}

	.recent-book-stats {
		display: flex;
		align-items: center;
		font-size: 12px;
		color: var(--text-secondary);
	}

	.recent-book-stats .stats-item {
		margin-right: 12px;
	}

	/* 加载更多样式 */
	.load-more-container {
		padding: 16px 0;
		text-align: center;
	}

	.load-more-trigger {
		display: inline-block;
		padding: 8px 24px;
		background-color: var(--primary-color);
		color: white;
		border-radius: 20px;
		font-size: 14px;
	}

	.load-more-text {
		color: white;
	}

	.no-more-container {
		padding: 16px 0;
		text-align: center;
	}

	.no-more-text {
		font-size: 14px;
		color: var(--text-secondary);
	}
</style>
