<template>
	<view>
		<!-- 更新对话框 -->
		<UpdateDialog
			:visible="showUpdateDialog"
			:updateInfo="updateInfo"
			:currentVersion="currentVersion"
			@close="closeUpdateDialog"
		/>
	</view>
</template>

<script>
	import { mapActions } from 'vuex';
	import preload from './utils/preload.js';
	import { initAppI18n } from './utils/i18n.js';
	import { initTabBarText } from './config/page-titles.js';
	import updateService from './services/updateService.js';
	import UpdateDialog from './components/UpdateDialog.vue';

	export default {
		components: {
			UpdateDialog
		},
		data() {
			return {
				showUpdateDialog: false,
				updateInfo: {},
				currentVersion: '1.0.0'
			}
		},
		onLaunch: function() {
			console.log('App Launch');

			// 强制重置为英文（面向美国客户）
			try {
				console.log('Force reset language to English...');
				uni.setStorageSync('app_language', 'en-US');
			} catch (error) {
				console.warn('Failed to reset language setting:', error);
			}

			// 初始化国际化
			try {
				console.log('Starting i18n initialization...');
				const currentLanguage = initAppI18n();
				console.log('I18n initialization completed, current language:', currentLanguage);

				// 初始化底部导航栏文本
				initTabBarText();
			} catch (error) {
				console.error('I18n initialization failed:', error);
			}

			// 初始化应用
			try {
				console.log('Starting app initialization...');
				this.initApp().then(() => {
					console.log('App initialization successful');
					// 启动数据预加载
					preload.startPreload();

					// 延迟检查更新（避免影响启动速度）
					setTimeout(() => {
						this.checkForUpdateOnLaunch();
					}, 3000);
				}).catch(error => {
					console.error('App initialization failed:', error);
				});
			} catch (error) {
				console.error('Failed to call initApp method:', error);
			}
		},
		onShow: function() {
			console.log('App Show');
		},
		onHide: function() {
			console.log('App Hide');
		},
		methods: {
			...mapActions(['initApp']),

			// 启动时检查更新
			async checkForUpdateOnLaunch() {
				try {
					console.log('Checking for updates on app launch...');
					const result = await updateService.checkUpdate();

					if (result.hasUpdate) {
						console.log('Update available:', result.updateInfo);
						this.updateInfo = result.updateInfo;
						this.currentVersion = result.currentVersion.version;

						console.log('App.vue - 设置updateInfo:', this.updateInfo);
						console.log('App.vue - 设置currentVersion:', this.currentVersion);

						// 如果是强制更新，立即显示对话框
						if (result.updateInfo.forceUpdate) {
							console.log('App.vue - 强制更新，显示对话框');
							this.showUpdateDialog = true;
						} else {
							// 可选更新，检查上次提醒时间
							const lastRemindTime = uni.getStorageSync('last_update_remind_time') || 0;
							const now = Date.now();
							const oneDayMs = 24 * 60 * 60 * 1000;

							// 每天最多提醒一次
							if (now - lastRemindTime > oneDayMs) {
								console.log('App.vue - 可选更新，显示对话框');
								this.showUpdateDialog = true;
								uni.setStorageSync('last_update_remind_time', now);
							} else {
								console.log('App.vue - 可选更新，但今天已提醒过');
							}
						}

						console.log('App.vue - showUpdateDialog:', this.showUpdateDialog);
					} else {
						console.log('No update available');
					}
				} catch (error) {
					console.error('Failed to check for updates on launch:', error);
					// 启动时检查更新失败不影响应用正常使用
				}
			},

			// 关闭更新对话框
			closeUpdateDialog() {
				this.showUpdateDialog = false;
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
		font-size: 28rpx;
		line-height: 1.5;
		color: #212529;
		background-color: #F8F9FA;
	}

	/* 通用样式 */
	.container {
		padding: 30rpx;
	}

	.flex-row {
		display: flex;
		flex-direction: row;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.flex-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.flex-around {
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.flex-1 {
		flex: 1;
	}

	/* 文本样式 */
	.text-primary {
		color: #1E5F74;
	}

	.text-secondary {
		color: #6C757D;
	}

	.text-success {
		color: #38B000;
	}

	.text-warning {
		color: #FF9F1C;
	}

	.text-danger {
		color: #D62828;
	}

	.text-info {
		color: #3A86FF;
	}

	.text-center {
		text-align: center;
	}

	.text-left {
		text-align: left;
	}

	.text-right {
		text-align: right;
	}

	.text-bold {
		font-weight: bold;
	}

	.text-ellipsis {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	/* 边距样式 */
	.m-0 { margin: 0; }
	.m-1 { margin: 10rpx; }
	.m-2 { margin: 20rpx; }
	.m-3 { margin: 30rpx; }
	.m-4 { margin: 40rpx; }
	.m-5 { margin: 50rpx; }

	.mt-0 { margin-top: 0; }
	.mt-1 { margin-top: 10rpx; }
	.mt-2 { margin-top: 20rpx; }
	.mt-3 { margin-top: 30rpx; }
	.mt-4 { margin-top: 40rpx; }
	.mt-5 { margin-top: 50rpx; }

	.mb-0 { margin-bottom: 0; }
	.mb-1 { margin-bottom: 10rpx; }
	.mb-2 { margin-bottom: 20rpx; }
	.mb-3 { margin-bottom: 30rpx; }
	.mb-4 { margin-bottom: 40rpx; }
	.mb-5 { margin-bottom: 50rpx; }

	.ml-0 { margin-left: 0; }
	.ml-1 { margin-left: 10rpx; }
	.ml-2 { margin-left: 20rpx; }
	.ml-3 { margin-left: 30rpx; }
	.ml-4 { margin-left: 40rpx; }
	.ml-5 { margin-left: 50rpx; }

	.mr-0 { margin-right: 0; }
	.mr-1 { margin-right: 10rpx; }
	.mr-2 { margin-right: 20rpx; }
	.mr-3 { margin-right: 30rpx; }
	.mr-4 { margin-right: 40rpx; }
	.mr-5 { margin-right: 50rpx; }

	.p-0 { padding: 0; }
	.p-1 { padding: 10rpx; }
	.p-2 { padding: 20rpx; }
	.p-3 { padding: 30rpx; }
	.p-4 { padding: 40rpx; }
	.p-5 { padding: 50rpx; }

	.pt-0 { padding-top: 0; }
	.pt-1 { padding-top: 10rpx; }
	.pt-2 { padding-top: 20rpx; }
	.pt-3 { padding-top: 30rpx; }
	.pt-4 { padding-top: 40rpx; }
	.pt-5 { padding-top: 50rpx; }

	.pb-0 { padding-bottom: 0; }
	.pb-1 { padding-bottom: 10rpx; }
	.pb-2 { padding-bottom: 20rpx; }
	.pb-3 { padding-bottom: 30rpx; }
	.pb-4 { padding-bottom: 40rpx; }
	.pb-5 { padding-bottom: 50rpx; }

	.pl-0 { padding-left: 0; }
	.pl-1 { padding-left: 10rpx; }
	.pl-2 { padding-left: 20rpx; }
	.pl-3 { padding-left: 30rpx; }
	.pl-4 { padding-left: 40rpx; }
	.pl-5 { padding-left: 50rpx; }

	.pr-0 { padding-right: 0; }
	.pr-1 { padding-right: 10rpx; }
	.pr-2 { padding-right: 20rpx; }
	.pr-3 { padding-right: 30rpx; }
	.pr-4 { padding-right: 40rpx; }
	.pr-5 { padding-right: 50rpx; }

	/* 按钮样式 */
	.btn {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		border-radius: 40rpx;
		text-align: center;
	}

	.btn-primary {
		background-color: #1E5F74;
		color: #FFFFFF;
	}

	.btn-secondary {
		background-color: #F0F2F5;
		color: #212529;
	}

	.btn-success {
		background-color: #38B000;
		color: #FFFFFF;
	}

	.btn-warning {
		background-color: #FF9F1C;
		color: #FFFFFF;
	}

	.btn-danger {
		background-color: #D62828;
		color: #FFFFFF;
	}

	.btn-info {
		background-color: #3A86FF;
		color: #FFFFFF;
	}

	.btn-block {
		width: 100%;
	}

	/* 卡片样式 */
	.card {
		background-color: #FFFFFF;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.card-header {
		padding: 20rpx;
		border-bottom: 1rpx solid #DEE2E6;
	}

	.card-body {
		padding: 20rpx;
	}

	.card-footer {
		padding: 20rpx;
		border-top: 1rpx solid #DEE2E6;
	}

	/* 表单样式 */
	.form-group {
		margin-bottom: 20rpx;
	}

	.form-label {
		font-size: 28rpx;
		color: #212529;
		margin-bottom: 10rpx;
		display: block;
	}

	.form-input {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #DEE2E6;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}

	.form-textarea {
		width: 100%;
		height: 200rpx;
		border: 1rpx solid #DEE2E6;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}

	/* 分割线 */
	.divider {
		height: 1rpx;
		background-color: #DEE2E6;
		margin: 20rpx 0;
	}

	/* 徽章 */
	.badge {
		display: inline-block;
		padding: 5rpx 15rpx;
		font-size: 22rpx;
		border-radius: 20rpx;
		background-color: #F0F2F5;
		color: #212529;
	}

	.badge-primary {
		background-color: #1E5F74;
		color: #FFFFFF;
	}

	.badge-success {
		background-color: #38B000;
		color: #FFFFFF;
	}

	.badge-warning {
		background-color: #FF9F1C;
		color: #FFFFFF;
	}

	.badge-danger {
		background-color: #D62828;
		color: #FFFFFF;
	}

	.badge-info {
		background-color: #3A86FF;
		color: #FFFFFF;
	}
</style>
