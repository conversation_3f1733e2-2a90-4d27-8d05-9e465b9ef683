/**
 * Start.io广告SDK工具类
 * 封装Start.io原生插件的调用方法
 */

class StartIOAdManager {
    constructor() {
        this.isInitialized = false;
        this.module = null;
        this.appId = '204660837';
    }

    /**
     * 获取原生插件模块
     */
    getModule() {
        if (!this.module) {
            // #ifdef APP-PLUS
            this.module = uni.requireNativePlugin('StartIOAd');
            // #endif
        }
        return this.module;
    }

    /**
     * 初始化Start.io SDK
     * @returns {Promise<Object>} 初始化结果
     */
    init() {
        return new Promise((resolve, reject) => {
            const module = this.getModule();
            if (!module) {
                const error = new Error('Start.io plugin not found');
                console.error('[StartIO] Plugin not found');
                reject(error);
                return;
            }

            console.log('[StartIO] Initializing SDK with App ID:', this.appId);
            
            module.initSDK({
                appId: this.appId
            }, (result) => {
                console.log('[StartIO] Init result:', result);
                
                if (result && result.success) {
                    this.isInitialized = true;
                    console.log('[StartIO] SDK initialized successfully');
                    resolve(result);
                } else {
                    const error = new Error(result?.message || 'SDK initialization failed');
                    console.error('[StartIO] SDK initialization failed:', error.message);
                    reject(error);
                }
            });
        });
    }

    /**
     * 检查SDK是否已初始化
     * @returns {Promise<boolean>} 初始化状态
     */
    isSDKInitialized() {
        return new Promise((resolve, reject) => {
            const module = this.getModule();
            if (!module) {
                resolve(false);
                return;
            }

            module.isSDKInitialized((result) => {
                console.log('[StartIO] SDK status check:', result);
                resolve(result && result.success && result.initialized);
            });
        });
    }

    /**
     * 显示插屏广告
     * @param {Object} options 广告选项
     * @returns {Promise<Object>} 显示结果
     */
    showInterstitialAd(options = {}) {
        return new Promise((resolve, reject) => {
            const module = this.getModule();
            if (!module) {
                const error = new Error('Start.io plugin not found');
                console.error('[StartIO] Plugin not found for interstitial ad');
                reject(error);
                return;
            }

            if (!this.isInitialized) {
                const error = new Error('SDK not initialized');
                console.error('[StartIO] SDK not initialized, cannot show interstitial ad');
                reject(error);
                return;
            }

            console.log('[StartIO] Showing interstitial ad with options:', options);
            
            module.showInterstitialAd(options, (result) => {
                console.log('[StartIO] Interstitial ad result:', result);
                
                if (result && result.success) {
                    console.log('[StartIO] Interstitial ad shown successfully');
                    resolve(result);
                } else {
                    const error = new Error(result?.message || 'Failed to show interstitial ad');
                    console.error('[StartIO] Failed to show interstitial ad:', error.message);
                    reject(error);
                }
            });
        });
    }

    /**
     * 获取App ID
     * @returns {Promise<string>} App ID
     */
    getAppId() {
        return new Promise((resolve, reject) => {
            const module = this.getModule();
            if (!module) {
                resolve(this.appId);
                return;
            }

            module.getAppId((result) => {
                console.log('[StartIO] Get App ID result:', result);
                if (result && result.success) {
                    resolve(result.appId || this.appId);
                } else {
                    resolve(this.appId);
                }
            });
        });
    }

    /**
     * 安全显示插屏广告（带错误处理）
     * @param {Object} options 广告选项
     * @param {Function} onSuccess 成功回调
     * @param {Function} onError 错误回调
     */
    async safeShowInterstitial(options = {}, onSuccess = null, onError = null) {
        try {
            // 检查SDK状态
            const isReady = await this.isSDKInitialized();
            if (!isReady) {
                console.log('[StartIO] SDK not ready, attempting to initialize...');
                await this.init();
            }

            // 显示广告
            const result = await this.showInterstitialAd(options);
            
            if (onSuccess) {
                onSuccess(result);
            }
            
            return result;
        } catch (error) {
            console.log('[StartIO] Safe show interstitial failed:', error.message);
            
            if (onError) {
                onError(error);
            } else {
                // 默认错误处理：静默失败，不影响用户体验
                console.log('[StartIO] Ad failed, continuing normal flow');
            }
            
            return null;
        }
    }
}

// 创建单例实例
const startIOManager = new StartIOAdManager();

// 导出便捷方法
export const StartIOAd = {
    /**
     * 初始化SDK
     */
    init: () => startIOManager.init(),

    /**
     * 检查SDK状态
     */
    isReady: () => startIOManager.isSDKInitialized(),

    /**
     * 显示插屏广告
     */
    showInterstitial: (options) => startIOManager.showInterstitialAd(options),

    /**
     * 安全显示插屏广告（推荐使用）
     */
    safeShowInterstitial: (options, onSuccess, onError) => 
        startIOManager.safeShowInterstitial(options, onSuccess, onError),

    /**
     * 获取App ID
     */
    getAppId: () => startIOManager.getAppId(),

    /**
     * 获取管理器实例（用于高级操作）
     */
    getManager: () => startIOManager
};

export default StartIOAd;
