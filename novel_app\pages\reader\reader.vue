<template>
	<view class="reader-container" :class="[`theme-${currentTheme}`, `font-${currentFont}`]" @click="handleScreenTap">
		<!-- 顶部控制栏 -->
		<view
			class="top-bar"
			:class="{ 'visible': isControlVisible }"
			:style="{ paddingTop: safeAreaTop + 'px' }"
		>
			<view class="back-btn" @click.stop="navigateBack">
				<text class="back-icon">←</text>
			</view>
			<view class="chapter-title">
				<text class="title-text">{{ currentChapterInfo.title }}</text>
			</view>
			<view class="more-btn" @click.stop="toggleMoreOptions">
				<text class="more-icon">⋮</text>
			</view>
		</view>

		<!-- 阅读内容区域 -->
		<scroll-view
			scroll-y
			class="content-scroll"
			:scroll-top="scrollTop"
			@scroll="handleScroll"
			@scrolltoupper="handleScrollToUpper"
			@scrolltolower="handleScrollToLower"
			:upper-threshold="50"
			:lower-threshold="50"
			:style="{
				fontSize: `${fontSize}px`,
				lineHeight: `${lineHeight}`,
				letterSpacing: `${letterSpacing}px`
			}"
		>
			<view class="continuous-content" :style="{ marginTop: (safeAreaTop + 30) + 'px' }">
				<!-- 顶部加载提示 -->
				<view class="loading-indicator" v-if="isLoadingPrev">
					<text class="loading-text">{{ $t('reader.loadingChapter') }}</text>
				</view>

				<!-- 连续章节内容 -->
				<view class="content-item" v-for="(item, index) in allContent" :key="`content-${index}`">
					<!-- 章节分隔线 -->
					<view class="chapter-divider" v-if="item.type === 'chapter-divider'">
						<view class="divider-line"></view>
					</view>

					<!-- 章节标题 -->
					<view class="chapter-title-section" v-else-if="item.type === 'chapter-title'">
						<text class="chapter-title-text">{{ item.content }}</text>
					</view>

					<!-- 段落内容 -->
					<view class="paragraph" v-else-if="item.type === 'paragraph'">
						<text class="paragraph-text">{{ processHtmlContent(item.content) }}</text>
					</view>
				</view>

				<!-- 底部加载提示 -->
				<view class="loading-indicator" v-if="isLoadingNext">
					<text class="loading-text">{{ $t('reader.loadingChapter') }}</text>
				</view>

				<!-- 结束提示 -->
				<view class="end-indicator" v-if="!isLoadingNext && !hasMoreNext">
					<text class="end-text">{{ $t('reader.endOfBook') }}</text>
				</view>
			</view>
		</scroll-view>

		<!-- 底部控制栏 -->
		<view class="bottom-bar" :class="{ 'visible': isControlVisible }">
			<view class="progress-info">
				<text class="progress-text">{{ progressText }}</text>
			</view>
			<view class="bottom-controls">
				<view class="control-btn" @click.stop="toggleChapterList">
					<text class="control-icon">📋</text>
					<text class="control-text">{{ $t('reader.catalog') }}</text>
				</view>
				<view class="control-btn" @click.stop="toggleSettings">
					<text class="control-icon">⚙️</text>
					<text class="control-text">{{ $t('reader.settings') }}</text>
				</view>
				<view class="control-btn" @click.stop="toggleDayNight">
					<text class="control-icon">{{ isDarkMode ? '☀️' : '🌙' }}</text>
					<text class="control-text">{{ isDarkMode ? $t('common.day') : $t('reader.nightMode') }}</text>
				</view>
			</view>
		</view>

		<!-- 设置面板 -->
		<view class="settings-panel" :class="{ 'visible': isSettingsVisible }" @click.stop>
			<view class="settings-header">
				<text class="settings-title">{{ $t('reader.settings') }}</text>
				<view class="close-btn" @click.stop="toggleSettings">
					<text class="close-icon">×</text>
				</view>
			</view>
			<view class="settings-content">
				<view class="setting-group">
					<text class="setting-label">{{ $t('reader.fontSize') }}</text>
					<view class="setting-controls">
						<view class="control-btn" @click.stop="changeFontSize(-1)">
							<text class="control-icon">-</text>
						</view>
						<text class="setting-value">{{ fontSize }}</text>
						<view class="control-btn" @click.stop="changeFontSize(1)">
							<text class="control-icon">+</text>
						</view>
					</view>
				</view>
				<view class="setting-group">
					<text class="setting-label">{{ $t('reader.lineHeight') }}</text>
					<view class="setting-controls">
						<view class="control-btn" @click.stop="changeLineHeight(-0.1)">
							<text class="control-icon">-</text>
						</view>
						<text class="setting-value">{{ lineHeight }}</text>
						<view class="control-btn" @click.stop="changeLineHeight(0.1)">
							<text class="control-icon">+</text>
						</view>
					</view>
				</view>
				<view class="setting-group">
					<text class="setting-label">{{ $t('common.letterSpacing') }}</text>
					<view class="setting-controls">
						<view class="control-btn" @click.stop="changeLetterSpacing(-0.5)">
							<text class="control-icon">-</text>
						</view>
						<text class="setting-value">{{ letterSpacing }}</text>
						<view class="control-btn" @click.stop="changeLetterSpacing(0.5)">
							<text class="control-icon">+</text>
						</view>
					</view>
				</view>
				<view class="setting-group">
					<text class="setting-label">{{ $t('common.theme') }}</text>
					<view class="theme-options">
						<view
							class="theme-option"
							v-for="theme in themes"
							:key="theme.id"
							:class="{ 'active': currentTheme === theme.id }"
							@click.stop="changeTheme(theme.id)"
						>
							<view class="theme-color" :style="{ backgroundColor: theme.bgColor }"></view>
							<text class="theme-name">{{ theme.name }}</text>
						</view>
					</view>
				</view>
				<view class="setting-group">
					<text class="setting-label">{{ $t('reader.fontFamily') }}</text>
					<view class="font-options">
						<view
							class="font-option"
							v-for="font in fonts"
							:key="font.id"
							:class="{ 'active': currentFont === font.id }"
							@click.stop="changeFont(font.id)"
						>
							<text class="font-name" :style="{ fontFamily: font.family }">{{ font.name }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 章节列表面板 -->
		<view class="chapter-list-panel" :class="{ 'visible': isChapterListVisible }" @click.stop>
			<view class="panel-header">
				<text class="panel-title">{{ $t('reader.catalog') }}</text>
				<view class="close-btn" @click.stop="toggleChapterList">
					<text class="close-icon">×</text>
				</view>
			</view>
			<scroll-view scroll-y class="chapter-list-scroll">
				<view
					class="chapter-list-item"
					v-for="(chapter, index) in chapters"
					:key="index"
					:class="{ 'active': chapter.id === currentChapterInfo.id }"
					@click.stop="selectChapter(chapter)"
				>
					<text class="chapter-list-title">{{ chapter.title }}</text>
				</view>
			</scroll-view>
		</view>


	</view>
</template>

<script>
	import chapterCache from '@/utils/chapterCache.js';
	import readingSessionManager from '@/utils/readingSession.js';
	import { i18nMixin } from '@/utils/i18n.js';

	export default {
		mixins: [i18nMixin],
		data() {
			return {
				bookId: null,
				chapterId: null,
				// 改为多章节模式
				chapterContents: [], // 存储多个章节的内容
				currentChapterIndex: 0, // 当前显示的章节索引
				chapters: [],
				isControlVisible: false,
				isSettingsVisible: false,
				isChapterListVisible: false,
				isMoreOptionsVisible: false,
				scrollTop: 0,
				scrollPosition: 0,
				scrollInfo: null, // 详细滚动信息
				totalHeight: 0,
				fontSize: 18,
				lineHeight: 1.8,
				letterSpacing: 0.5,
				currentTheme: 'default',
				currentFont: 'default',
				isDarkMode: false,
				controlHideTimer: null,
				chapterUpdateTimer: null, // 章节更新防抖定时器
				// 预加载相关
				isLoadingNext: false,
				isLoadingPrev: false,
				loadThreshold: 200, // 距离底部/顶部多少像素时开始加载
				hasMoreNext: true, // 是否还有下一章
				hasMorePrev: true, // 是否还有上一章
				themes: [],
				fonts: [],
				// 系统信息
				statusBarHeight: 0, // 状态栏高度
				safeAreaTop: 0 // 安全区域顶部高度
			}
		},
		computed: {
			progressText() {
				// 基于章节统计计算全书阅读进度
				if (this.chapters.length === 0) {
					return '0%';
				}

				// 找到当前章节在所有章节中的位置
				const currentChapterIndex = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));

				if (currentChapterIndex === -1) {
					return '0%';
				}

				// 计算基于章节的进度：当前章节位置 / 总章节数
				// 加上当前章节内的滚动进度作为微调
				const chapterProgress = currentChapterIndex / this.chapters.length;
				const inChapterProgress = this.scrollPosition / this.chapters.length;
				const totalProgress = (chapterProgress + inChapterProgress) * 100;

				// 确保进度在0-100之间
				const progress = Math.max(0, Math.min(Math.floor(totalProgress), 100));
				return `${progress}%`;
			},
			// 当前显示的章节信息 - 基于可见内容精确匹配
			currentChapterInfo() {
				// 在多章节模式下，使用精确的内容匹配来确定当前可见章节
				if (this.chapterContents.length > 1 && this.scrollInfo) {
					const currentChapterId = this.getCurrentChapterByContent();

					// 先在已加载的章节内容中查找
					const loadedChapter = this.chapterContents.find(ch => String(ch.id) === String(currentChapterId));
					if (loadedChapter) {
						return loadedChapter;
					}

					// 如果已加载章节中没有，再从章节列表中查找
					if (this.chapters.length > 0) {
						const chapterFromList = this.chapters.find(ch => String(ch.id) === String(currentChapterId));
						if (chapterFromList) {
							return chapterFromList;
						}
					}
				}

				// 单章节模式或无法精确匹配时，优先使用当前章节ID对应的章节信息
				if (this.chapters.length > 0) {
					const currentChapter = this.chapters.find(ch => String(ch.id) === String(this.chapterId));
					if (currentChapter) {
						return currentChapter;
					}
				}

				// 最后回退到已加载的章节内容
				if (this.chapterContents.length > 0) {
					const chapter = this.chapterContents[this.currentChapterIndex] || this.chapterContents[0];
					return chapter;
				}

				return {
					id: this.chapterId,
					title: this.$t('common.loading'),
					content: []
				};
			},
			// 所有章节的合并内容
			allContent() {
				let allParagraphs = [];
				this.chapterContents.forEach((chapter, index) => {
					// 添加章节标题
					if (index > 0) {
						allParagraphs.push({
							type: 'chapter-divider',
							content: '',
							chapterIndex: index
						});
					}
					allParagraphs.push({
						type: 'chapter-title',
						content: chapter.title,
						chapterIndex: index
					});
					// 添加章节内容
					chapter.content.forEach(paragraph => {
						allParagraphs.push({
							type: 'paragraph',
							content: paragraph,
							chapterIndex: index
						});
					});
				});
				return allParagraphs;
			}
		},
		onLoad(options) {
			this.bookId = options.bookId;
			this.chapterId = options.chapterId;

			// 获取系统信息
			this.getSystemInfo();

			// 初始化国际化数据
			this.initializeI18nData();

			// 加载阅读设置
			this.loadReadingSettings();

			// 初始化阅读器
			this.initializeReader();

			// 显示控制栏，并设置定时隐藏
			this.showControls();
		},
		onUnload() {
			console.log('📱 阅读器页面卸载，开始清理资源');

			try {
				// 页面卸载时结束阅读会话
				readingSessionManager.endSession();

				// 清理定时器
				if (this.controlHideTimer) {
					clearTimeout(this.controlHideTimer);
					this.controlHideTimer = null;
				}
				if (this.chapterUpdateTimer) {
					clearTimeout(this.chapterUpdateTimer);
					this.chapterUpdateTimer = null;
				}

				// 清理数据
				this.chapterContents = [];
				this.chapters = [];

				console.log('✅ 阅读器资源清理完成');
			} catch (error) {
				console.error('❌ 阅读器资源清理失败:', error);
			}
		},
		onHide() {
			// 页面隐藏时暂停阅读
			readingSessionManager.pauseReading();
		},
		onShow() {
			// 页面显示时恢复阅读
			readingSessionManager.resumeReading();
		},
		methods: {
			// 获取系统信息
			getSystemInfo() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight || 0;
					this.safeAreaTop = systemInfo.safeAreaInsets?.top || this.statusBarHeight;

					console.log('📱 系统信息获取成功:', {
						statusBarHeight: this.statusBarHeight,
						safeAreaTop: this.safeAreaTop
					});
				} catch (error) {
					console.error('❌ 获取系统信息失败:', error);
					// 设置默认值
					this.statusBarHeight = 44; // iOS默认状态栏高度
					this.safeAreaTop = 44;
				}
			},

			// 初始化国际化数据
			initializeI18nData() {
				this.themes = [
					{ id: 'default', name: this.$t('reader.themeDefault'), bgColor: '#FFFFFF' },
					{ id: 'cream', name: this.$t('reader.themeEyeCare'), bgColor: '#F8F3E8' },
					{ id: 'dark', name: this.$t('reader.themeDark'), bgColor: '#222222' },
					{ id: 'green', name: this.$t('reader.themeGreen'), bgColor: '#E8F3E8' }
				];
				this.fonts = [
					{ id: 'default', name: this.$t('reader.fontDefault'), family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' },
					{ id: 'serif', name: this.$t('reader.fontSerif'), family: '"Times New Roman", "SimSun", serif' },
					{ id: 'mono', name: this.$t('reader.fontMono'), family: '"Courier New", "Consolas", monospace' }
				];
			},

			// 初始化阅读器
			async initializeReader() {
				console.log('=== 初始化无缝阅读器 ===');

				try {
					// 1. 加载章节列表
					await this.loadChapterList();

					// 2. 加载当前章节
					await this.loadCurrentChapter();

					// 3. 开始预加载相邻章节
					this.startPreloading();

					// 4. 启动阅读会话
					await this.startReadingSession();

					// 5. 恢复阅读位置
					await this.restoreReadingPosition();

					console.log('✅ 阅读器初始化完成');
				} catch (error) {
					console.error('❌ 阅读器初始化失败:', error);
					uni.showToast({
						title: this.$t('common.error'),
						icon: 'none'
					});
				}
			},

			// 启动阅读会话
			async startReadingSession() {
				try {
					// 获取书籍信息
					const bookInfo = await this.getBookInfo();

					// 获取当前章节信息
					const chapterInfo = this.getCurrentChapterInfo();

					// 开始阅读会话
					readingSessionManager.startSession(bookInfo, chapterInfo);

					console.log('📖 阅读会话已启动');
				} catch (error) {
					console.error('启动阅读会话失败:', error);
				}
			},

			// 获取书籍信息
			async getBookInfo() {
				try {
					// 尝试从API获取书籍详情
					const bookDetail = await this.$api.book.getBookDetail(this.bookId);
					return {
						id: this.bookId,
						title: bookDetail.title || this.$t('bookDetail.unknownTitle'),
						author: bookDetail.author || this.$t('bookDetail.unknownAuthor'),
						category: bookDetail.category?.name || bookDetail.category || this.$t('common.unknown')
					};
				} catch (error) {
					console.warn('获取书籍信息失败，使用默认信息:', error);
					return {
						id: this.bookId,
						title: this.$t('bookDetail.unknownTitle'),
						author: this.$t('bookDetail.unknownAuthor'),
						category: this.$t('common.unknown')
					};
				}
			},

			/**
			 * 恢复阅读位置
			 */
			async restoreReadingPosition() {
				try {
					console.log('📍 开始恢复阅读位置...');

					// 首先尝试从本地存储获取阅读进度
					const localProgress = uni.getStorageSync(`readingProgress_${this.bookId}`);
					let readingProgress = null;

					if (localProgress) {
						try {
							readingProgress = JSON.parse(localProgress);
							console.log('📖 从本地存储获取到阅读进度:', readingProgress);
						} catch (e) {
							console.warn('解析本地阅读进度失败:', e);
						}
					}

					// 如果用户已登录且本地没有进度，尝试从服务器获取
					if (!readingProgress) {
						const token = uni.getStorageSync('token');
						if (token) {
							try {
								console.log('📡 尝试从服务器获取阅读进度...');
								const serverProgress = await this.$api.reading.getReadingProgress(this.bookId);

								if (serverProgress && serverProgress.chapter_id) {
									console.log('📖 从服务器获取到阅读进度:', serverProgress);
									readingProgress = {
										bookId: this.bookId,
										chapterId: serverProgress.chapter_id,
										position: serverProgress.position || 0,
										percentage: serverProgress.percentage || 0,
										timestamp: new Date(serverProgress.last_read_at || Date.now()).getTime()
									};
								}
							} catch (error) {
								console.warn('从服务器获取阅读进度失败:', error);
							}
						}
					}

					// 如果有阅读进度且章节ID匹配，恢复滚动位置
					if (readingProgress && String(readingProgress.chapterId) === String(this.chapterId)) {
						console.log('📍 恢复到上次阅读位置:', readingProgress.position);

						// 等待DOM渲染完成后恢复滚动位置
						this.$nextTick(() => {
							setTimeout(() => {
								// 计算实际的滚动位置
								const scrollTop = readingProgress.position * this.totalHeight || 0;
								this.scrollTop = scrollTop;
								console.log('✅ 阅读位置恢复完成，滚动到:', scrollTop);
							}, 300); // 给内容渲染一些时间
						});
					} else {
						console.log('📍 无匹配的阅读进度，从章节开头开始');
					}

				} catch (error) {
					console.error('恢复阅读位置失败:', error);
				}
			},

			// 获取当前章节信息
			getCurrentChapterInfo() {
				const currentChapter = this.currentChapterInfo;
				return {
					id: this.chapterId,
					title: currentChapter.title || this.$t('reader.unknownChapter'),
					index: this.getCurrentChapterIndex()
				};
			},

			// 获取当前章节索引
			getCurrentChapterIndex() {
				if (this.chapters.length === 0) return 0;

				const index = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));
				return index >= 0 ? index + 1 : 0;
			},

			// 处理HTML内容，将HTML标签转换为Unicode字符
			processHtmlContent(content) {
				if (!content || typeof content !== 'string') {
					return content;
				}

				let processedContent = content;

				// 第一步：处理HTML实体
				const htmlEntities = {
					'&nbsp;': ' ',
					'&lt;': '<',
					'&gt;': '>',
					'&amp;': '&',
					'&quot;': '"',
					'&#39;': "'",
					'&apos;': "'",
					'&copy;': '©',
					'&reg;': '®',
					'&trade;': '™'
				};

				Object.keys(htmlEntities).forEach(entity => {
					const char = htmlEntities[entity];
					const regex = new RegExp(entity, 'gi');
					processedContent = processedContent.replace(regex, char);
				});

				// 第二步：处理脚注结构
				// 处理脚注标题
				processedContent = processedContent.replace(/<b>\s*Footnote:\s*<\/b>/gi, '\nFootnote:\n');
				processedContent = processedContent.replace(/<b>\s*footnote:\s*<\/b>/gi, '\nFootnote:\n');

				// 第三步：处理列表结构
				// 处理有序列表
				processedContent = processedContent.replace(/<ol[^>]*>/gi, '\n');
				processedContent = processedContent.replace(/<\/ol>/gi, '\n');

				// 处理无序列表
				processedContent = processedContent.replace(/<ul[^>]*>/gi, '\n');
				processedContent = processedContent.replace(/<\/ul>/gi, '\n');

				// 处理列表项 - 添加编号
				let listItemCounter = 1;
				processedContent = processedContent.replace(/<li[^>]*>(.*?)<\/li>/gi, (match, content) => {
					const cleanContent = content.trim();
					if (cleanContent) {
						return `\n${listItemCounter++}. ${cleanContent}`;
					}
					return '';
				});

				// 第四步：处理格式标签
				const formatTags = {
					'<b>': '',
					'</b>': '',
					'<strong>': '',
					'</strong>': '',
					'<em>': '',
					'</em>': '',
					'<i>': '',
					'</i>': '',
					'<u>': '',
					'</u>': '',
					'<br>': '\n',
					'<br/>': '\n',
					'<br />': '\n',
					'<p>': '\n',
					'</p>': '\n'
				};

				Object.keys(formatTags).forEach(tag => {
					const replacement = formatTags[tag];
					const regex = new RegExp(tag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
					processedContent = processedContent.replace(regex, replacement);
				});

				// 第五步：处理上标和下标
				// HTML标签到Unicode字符的映射
				const htmlToUnicode = {
					// 上标数字
					'<sup>0</sup>': '⁰',
					'<sup>1</sup>': '¹',
					'<sup>2</sup>': '²',
					'<sup>3</sup>': '³',
					'<sup>4</sup>': '⁴',
					'<sup>5</sup>': '⁵',
					'<sup>6</sup>': '⁶',
					'<sup>7</sup>': '⁷',
					'<sup>8</sup>': '⁸',
					'<sup>9</sup>': '⁹',
					// 下标数字
					'<sub>0</sub>': '₀',
					'<sub>1</sub>': '₁',
					'<sub>2</sub>': '₂',
					'<sub>3</sub>': '₃',
					'<sub>4</sub>': '₄',
					'<sub>5</sub>': '₅',
					'<sub>6</sub>': '₆',
					'<sub>7</sub>': '₇',
					'<sub>8</sub>': '₈',
					'<sub>9</sub>': '₉'
				};

				// 替换所有映射的HTML标签
				Object.keys(htmlToUnicode).forEach(htmlTag => {
					const unicodeChar = htmlToUnicode[htmlTag];
					// 使用全局替换，忽略大小写
					const regex = new RegExp(htmlTag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
					processedContent = processedContent.replace(regex, unicodeChar);
				});

				// 处理任意内容的上标标签（如果上面的映射没有覆盖）
				processedContent = processedContent.replace(/<sup>(.*?)<\/sup>/gi, (match, content) => {
					// 如果是单个数字，尝试转换为Unicode上标
					if (/^\d$/.test(content)) {
						const superscriptMap = {
							'0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
							'5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹'
						};
						return superscriptMap[content] || content;
					}
					// 其他内容直接返回，去掉标签
					return content;
				});

				// 处理任意内容的下标标签
				processedContent = processedContent.replace(/<sub>(.*?)<\/sub>/gi, (match, content) => {
					// 如果是单个数字，尝试转换为Unicode下标
					if (/^\d$/.test(content)) {
						const subscriptMap = {
							'0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
							'5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉'
						};
						return subscriptMap[content] || content;
					}
					// 其他内容直接返回，去掉标签
					return content;
				});

				// 第六步：清理多余的换行和空格
				processedContent = processedContent
					.replace(/\n\s*\n\s*\n/g, '\n\n') // 多个换行合并为两个
					.replace(/^\s+|\s+$/g, '') // 去掉首尾空白
					.replace(/[ \t]+/g, ' '); // 多个空格合并为一个

				return processedContent;
			},

			// 加载当前章节
			async loadCurrentChapter() {
				console.log('=== 加载当前章节 ===', this.chapterId);

				try {
					const chapterContent = await chapterCache.getChapterContent(
						this.bookId,
						this.chapterId,
						this.$api.book.getChapterContent.bind(this.$api.book)
					);

					const processedContent = this.processChapterContent(chapterContent);

					// 初始化章节内容数组
					this.chapterContents = [processedContent];
					this.currentChapterIndex = 0;

					console.log('✅ 当前章节加载完成:', processedContent.title);
				} catch (error) {
					console.error('❌ 加载当前章节失败:', error);
					// 使用默认内容
					const defaultContent = this.getDefaultChapterContent();
					this.chapterContents = [defaultContent];
					this.currentChapterIndex = 0;
				}
			},

			// 处理章节内容
			processChapterContent(chapterData) {
				if (!chapterData) {
					return this.getDefaultChapterContent();
				}

				console.log('=== 处理章节内容 ===');
				console.log('chapterData.content_type:', chapterData.content_type);

				// 处理内容格式
				let content = [];
				const contentType = chapterData.content_type || 'text';

				if (chapterData.content) {
					if (contentType === 'images') {
						// 处理图片章节
						if (Array.isArray(chapterData.content)) {
							content = chapterData.content.map(img => `[图片: ${img}]`);
						} else {
							content = ['[图片内容暂不支持显示]'];
						}
					} else {
						// 处理文本章节
						if (Array.isArray(chapterData.content)) {
							content = chapterData.content;
						} else if (typeof chapterData.content === 'string') {
							if (chapterData.content.trim()) {
								content = chapterData.content
									.split(/\r?\n\r?\n|\n\n/)
									.map(p => p.replace(/\r?\n/g, ' ').trim())
									.filter(p => p);
							}
						}
					}
				}

				// 如果没有内容，使用错误提示
				if (content.length === 0) {
					content = this.getErrorContent(contentType);
				}

				return {
					id: chapterData.id || this.chapterId,
					title: chapterData.title || `第${this.chapterId}章`,
					content: content,
					contentType: contentType
				};
			},

			// 获取错误内容
			getErrorContent(contentType) {
				const errorMessages = [
					'抱歉，该章节内容暂时无法加载。',
					'',
					'可能的原因：',
					'• 章节内容尚未发布或正在更新中',
					'• 服务器数据暂时不可用',
					'• 网络连接不稳定',
					'',
					'建议操作：',
					'• 检查网络连接后重试',
					'• 稍后再次尝试阅读',
					'• 如问题持续存在，请联系客服'
				];

				if (contentType === 'images') {
					errorMessages.splice(3, 0, '• 图片章节加载失败');
				}

				return errorMessages;
			},

			// 获取默认章节内容
			getDefaultChapterContent() {
				return {
					id: this.chapterId,
					title: `第${this.chapterId}章`,
					content: [
						'章节内容加载失败',
						'',
						'可能的原因：',
						'• 网络连接不稳定',
						'• 服务器暂时不可用',
						'• 章节数据异常',
						'',
						'建议操作：',
						'• 检查网络连接',
						'• 重新进入阅读页面',
						'• 稍后重试',
						'• 如问题持续存在，请联系客服'
					],
					contentType: 'text'
				};
			},

			// 检查章节连续性
			checkChapterContinuity() {
				if (this.chapters.length === 0) return;

				console.log('=== 检查章节连续性 ===');
				console.log(`📚 书籍ID: ${this.bookId}`);
				console.log(`📖 当前章节ID: ${this.chapterId}`);
				console.log(`📄 总章节数: ${this.chapters.length}`);

				// 查找当前章节在列表中的位置
				const currentIndex = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));
				if (currentIndex >= 0) {
					console.log(`🎯 当前章节在列表中的位置: ${currentIndex + 1}/${this.chapters.length}`);
					console.log(`📝 当前章节信息: ID:${this.chapters[currentIndex].id}, Index:${this.chapters[currentIndex].index}, Title:${this.chapters[currentIndex].title}`);
				} else {
					console.warn(`⚠️ 当前章节ID ${this.chapterId} 在章节列表中未找到！`);
				}

				// 显示当前章节周围的章节信息
				const contextRange = 5; // 显示前后5章
				const startIndex = Math.max(0, currentIndex - contextRange);
				const endIndex = Math.min(this.chapters.length - 1, currentIndex + contextRange);

				console.log(`\n📋 当前章节周围的章节信息 (${startIndex + 1}-${endIndex + 1}):`);
				for (let i = startIndex; i <= endIndex; i++) {
					const chapter = this.chapters[i];
					const marker = i === currentIndex ? ' 👈 当前' : '';
					console.log(`  ${i + 1}. ID:${chapter.id}, Index:${chapter.index}, Title:${chapter.title}${marker}`);
				}

				// 检查是否有缺失的章节
				const missingChapters = [];
				for (let i = 1; i < this.chapters.length; i++) {
					const prevChapter = this.chapters[i - 1];
					const currentChapter = this.chapters[i];

					const prevIndex = parseInt(prevChapter.index) || parseInt(prevChapter.id) || 0;
					const currentIndex = parseInt(currentChapter.index) || parseInt(currentChapter.id) || 0;

					// 如果索引不连续，记录缺失的章节
					if (currentIndex - prevIndex > 1) {
						for (let missing = prevIndex + 1; missing < currentIndex; missing++) {
							missingChapters.push(missing);
						}
					}
				}

				if (missingChapters.length > 0) {
					console.warn('⚠️ 发现缺失的章节:', missingChapters);
					console.warn('这可能导致章节跳跃问题');

					// 显示缺失章节的详细信息
					missingChapters.forEach(missing => {
						const beforeChapter = this.chapters.find(ch => (parseInt(ch.index) || parseInt(ch.id)) === missing - 1);
						const afterChapter = this.chapters.find(ch => (parseInt(ch.index) || parseInt(ch.id)) === missing + 1);

						console.log(`\n缺失章节 ${missing} 的上下文:`);
						if (beforeChapter) {
							console.log(`  前一章: ID:${beforeChapter.id}, Index:${beforeChapter.index}, Title:${beforeChapter.title}`);
						}
						console.log(`  缺失: 第${missing}章`);
						if (afterChapter) {
							console.log(`  后一章: ID:${afterChapter.id}, Index:${afterChapter.index}, Title:${afterChapter.title}`);
						}
					});

					// 可以选择性地显示用户提示
					if (missingChapters.length <= 5) {
						uni.showToast({
							title: `检测到缺失章节: ${missingChapters.join(', ')}`,
							icon: 'none',
							duration: 3000
						});
					}
				} else {
					console.log('✅ 章节连续性检查通过');
				}
			},

			// 开始预加载
			startPreloading() {
				console.log('🚀 开始预加载相邻章节');
				chapterCache.preloadChapters(
					this.bookId,
					this.chapterId,
					this.chapters,
					this.$api.book.getChapterContent.bind(this.$api.book)
				);
			},

			// 加载下一章节
			async loadNextChapter() {
				if (this.isLoadingNext) return;

				console.log('=== 开始加载下一章 ===');
				console.log('当前章节ID:', this.chapterId);

				const currentIndex = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));
				console.log('当前章节在列表中的索引:', currentIndex);

				if (currentIndex === -1) {
					console.error('❌ 在章节列表中找不到当前章节');
					return;
				}

				if (currentIndex >= this.chapters.length - 1) {
					console.log('📚 已到达最后一章');
					this.hasMoreNext = false;
					return;
				}

				this.isLoadingNext = true;
				const nextChapter = this.chapters[currentIndex + 1];

				console.log('📖 准备加载下一章:');
				console.log('  - 索引:', currentIndex + 1);
				console.log('  - ID:', nextChapter.id);
				console.log('  - 标题:', nextChapter.title);
				console.log('  - Index字段:', nextChapter.index);

				try {
					const chapterContent = await chapterCache.getChapterContent(
						this.bookId,
						nextChapter.id,
						this.$api.book.getChapterContent.bind(this.$api.book)
					);

					const processedContent = this.processChapterContent(chapterContent);

					// 添加到章节内容数组
					this.chapterContents.push(processedContent);

					// 更新当前章节ID
					this.chapterId = nextChapter.id;

					console.log('✅ 下一章加载完成:', processedContent.title);

					// 继续预加载
					this.startPreloading();

				} catch (error) {
					console.error('❌ 加载下一章失败:', error);
					uni.showToast({
						title: '加载下一章失败',
						icon: 'none'
					});
				} finally {
					this.isLoadingNext = false;
				}
			},

			// 加载上一章节
			async loadPrevChapter() {
				if (this.isLoadingPrev) return;

				console.log('=== 开始加载上一章 ===');
				console.log('当前章节ID:', this.chapterId);

				const currentIndex = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));
				console.log('当前章节在列表中的索引:', currentIndex);

				if (currentIndex === -1) {
					console.error('❌ 在章节列表中找不到当前章节');
					return;
				}

				if (currentIndex <= 0) {
					console.log('📚 已到达第一章');
					this.hasMorePrev = false;
					return;
				}

				this.isLoadingPrev = true;
				const prevChapter = this.chapters[currentIndex - 1];

				console.log('📖 准备加载上一章:');
				console.log('  - 索引:', currentIndex - 1);
				console.log('  - ID:', prevChapter.id);
				console.log('  - 标题:', prevChapter.title);
				console.log('  - Index字段:', prevChapter.index);

				try {
					// 记录当前滚动位置
					const currentScrollTop = this.scrollTop;
					console.log('📍 当前滚动位置:', currentScrollTop);

					const chapterContent = await chapterCache.getChapterContent(
						this.bookId,
						prevChapter.id,
						this.$api.book.getChapterContent.bind(this.$api.book)
					);

					const processedContent = this.processChapterContent(chapterContent);

					// 计算新章节的预估高度（用于调整滚动位置）
					const estimatedHeight = this.estimateChapterHeight(processedContent);
					console.log('📏 预估新章节高度:', estimatedHeight);

					// 添加到章节内容数组开头
					this.chapterContents.unshift(processedContent);
					this.currentChapterIndex++;

					// 更新当前章节ID为上一章
					this.chapterId = prevChapter.id;

					console.log('✅ 上一章加载完成:', processedContent.title);

					// 等待DOM更新后调整滚动位置
					this.$nextTick(() => {
						// 调整滚动位置，确保用户能看到新加载的内容
						const newScrollTop = currentScrollTop + estimatedHeight;
						console.log('📍 调整滚动位置到:', newScrollTop);

						this.scrollTop = newScrollTop;

						// 小延迟后再次确保滚动位置正确
						setTimeout(() => {
							this.scrollTop = newScrollTop + 1; // 微调触发重新渲染
							console.log('📍 最终滚动位置:', newScrollTop + 1);
						}, 100);
					});

				} catch (error) {
					console.error('❌ 加载上一章失败:', error);
					uni.showToast({
						title: '加载上一章失败',
						icon: 'none'
					});
				} finally {
					this.isLoadingPrev = false;
				}
			},

			// 估算章节高度（用于滚动位置调整）
			estimateChapterHeight(chapterContent) {
				if (!chapterContent || !chapterContent.content) {
					return 800; // 默认高度
				}

				// 基础计算参数
				const lineHeight = this.fontSize * this.lineHeight; // 行高
				const charsPerLine = Math.floor(350 / this.fontSize); // 每行大约字符数（基于容器宽度）

				// 计算总字符数
				let totalChars = 0;
				if (Array.isArray(chapterContent.content)) {
					totalChars = chapterContent.content.reduce((sum, paragraph) => {
						return sum + (paragraph ? paragraph.length : 0);
					}, 0);
				} else if (typeof chapterContent.content === 'string') {
					totalChars = chapterContent.content.length;
				}

				// 估算行数
				const estimatedLines = Math.ceil(totalChars / charsPerLine);

				// 计算高度：行数 * 行高 + 章节标题高度 + 段落间距
				const titleHeight = 60; // 章节标题高度
				const paragraphSpacing = chapterContent.content.length * 10; // 段落间距
				const contentHeight = estimatedLines * lineHeight;

				const totalHeight = titleHeight + contentHeight + paragraphSpacing;

				console.log('📏 章节高度估算:');
				console.log('  - 总字符数:', totalChars);
				console.log('  - 估算行数:', estimatedLines);
				console.log('  - 内容高度:', contentHeight);
				console.log('  - 总高度:', totalHeight);

				return Math.max(totalHeight, 400); // 最小高度400px
			},

			// 处理滚动到顶部
			handleScrollToUpper() {
				console.log('📜 滚动到顶部，尝试加载上一章');
				if (this.hasMorePrev && !this.isLoadingPrev) {
					this.loadPrevChapter();
				}
			},

			// 处理滚动到底部
			handleScrollToLower() {
				console.log('📜 滚动到底部，尝试加载下一章');
				if (this.hasMoreNext && !this.isLoadingNext) {
					this.loadNextChapter();
				}
			},



			// 加载章节列表（返回Promise）
			loadChapterList() {
				console.log('=== 开始加载章节列表 ===');
				console.log('书籍ID:', this.bookId);

				return this.$api.book.getBookChapters(this.bookId)
					.then(result => {
						console.log('=== 章节列表API响应 ===');
						console.log('响应数据:', JSON.stringify(result, null, 2));

						if (result && result.chapters) {
							// 确保章节按index字段正确排序
							this.chapters = result.chapters.sort((a, b) => {
								// 优先使用index字段排序，如果没有则使用id
								const indexA = parseInt(a.index) || parseInt(a.id) || 0;
								const indexB = parseInt(b.index) || parseInt(b.id) || 0;
								return indexA - indexB;
							});

							console.log('章节列表加载成功，共', this.chapters.length, '章');

							// 检查章节连续性
							this.checkChapterContinuity();

							// 更新边界状态
							const currentIndex = this.chapters.findIndex(chapter =>
								String(chapter.id) === String(this.chapterId)
							);
							this.hasMorePrev = currentIndex > 0;
							this.hasMoreNext = currentIndex < this.chapters.length - 1;

							console.log('当前章节索引:', currentIndex);
							console.log('当前章节ID:', this.chapterId);
							if (currentIndex >= 0) {
								console.log('当前章节信息:', this.chapters[currentIndex]);
								if (currentIndex > 0) {
									console.log('上一章信息:', this.chapters[currentIndex - 1]);
								}
								if (currentIndex < this.chapters.length - 1) {
									console.log('下一章信息:', this.chapters[currentIndex + 1]);
								}
							}
							console.log('有上一章:', this.hasMorePrev);
							console.log('有下一章:', this.hasMoreNext);

							return this.chapters;
						} else {
							console.warn('章节列表数据格式不正确');
							this.chapters = [];
							return [];
						}
					})
					.catch(error => {
						console.error('获取章节列表失败:', error);
						this.chapters = [];
						throw error;
					});
			},
			loadReadingSettings() {
				try {
					const settings = uni.getStorageSync('readingSettings');
					if (settings) {
						let parsedSettings;

						// 检查数据类型并安全解析
						if (typeof settings === 'string') {
							try {
								parsedSettings = JSON.parse(settings);
							} catch (parseError) {
								console.error('JSON解析失败:', parseError);
								console.log('原始设置数据:', settings);
								// 清除损坏的数据
								uni.removeStorageSync('readingSettings');
								return;
							}
						} else if (typeof settings === 'object' && settings !== null) {
							parsedSettings = settings;
						} else {
							console.warn('无效的设置数据类型:', typeof settings);
							return;
						}

						// 安全地应用设置
						this.fontSize = parsedSettings.fontSize || 18;
						this.lineHeight = parsedSettings.lineHeight || 1.8;
						this.letterSpacing = parsedSettings.letterSpacing || 0.5;

						// 处理主题设置（兼容旧版本）
						this.currentTheme = parsedSettings.theme || 'default';
						this.currentFont = parsedSettings.font || parsedSettings.fontFamily || 'default';
						this.isDarkMode = this.currentTheme === 'dark';

						// 应用字体家族设置
						if (parsedSettings.fontFamily && parsedSettings.fontFamily !== 'system') {
							this.currentFont = parsedSettings.fontFamily;
						}

						console.log('✅ 阅读设置加载成功:', parsedSettings);
					} else {
						console.log('📖 使用默认阅读设置');
					}
				} catch (e) {
					console.error('❌ 加载阅读设置失败:', e);
					// 清除可能损坏的设置数据
					try {
						uni.removeStorageSync('readingSettings');
						console.log('🗑️ 已清除损坏的阅读设置数据');
					} catch (clearError) {
						console.error('清除设置数据失败:', clearError);
					}
				}
			},
			saveReadingSettings() {
				// 保存阅读设置到本地存储，兼容阅读设置页面的格式
				const settings = {
					fontSize: this.fontSize,
					lineHeight: this.lineHeight,
					letterSpacing: this.letterSpacing,
					theme: this.currentTheme,
					font: this.currentFont,
					fontFamily: this.currentFont, // 兼容阅读设置页面
					autoSave: true, // 默认开启自动保存
					keepScreenOn: false, // 默认关闭屏幕常亮
					volumeKeyPage: false, // 默认关闭音量键翻页
					showProgress: true // 默认显示阅读进度
				};
				uni.setStorageSync('readingSettings', settings); // 直接保存对象，不需要JSON.stringify
			},
			handleScreenTap(e) {
				// 获取点击位置
				const { clientX, clientY } = e.touches[0];
				const windowHeight = uni.getSystemInfoSync().windowHeight;

				// 定义屏幕区域
				const topRegion = windowHeight * 0.3;
				const bottomRegion = windowHeight * 0.7;

				// 中间区域点击 - 显示/隐藏控制栏
				if (clientY > topRegion && clientY < bottomRegion) {
					this.toggleControls();
				}
			},
			toggleControls() {
				this.isControlVisible = !this.isControlVisible;

				if (this.isControlVisible) {
					this.showControls();
				} else {
					this.hideControls();
				}
			},
			showControls() {
				this.isControlVisible = true;

				// 清除之前的定时器
				if (this.controlHideTimer) {
					clearTimeout(this.controlHideTimer);
				}

				// 设置新的定时器，3秒后自动隐藏
				this.controlHideTimer = setTimeout(() => {
					this.hideControls();
				}, 3000);
			},
			hideControls() {
				this.isControlVisible = false;
				this.isSettingsVisible = false;
				this.isChapterListVisible = false;
				this.isMoreOptionsVisible = false;
			},
			handleScroll(e) {
				// 计算滚动进度
				const scrollTop = e.detail.scrollTop || 0;
				const scrollHeight = e.detail.scrollHeight || 1;
				const clientHeight = e.detail.clientHeight || uni.getSystemInfoSync().windowHeight;

				// 修正进度计算逻辑
				const maxScrollTop = Math.max(scrollHeight - clientHeight, 1);
				this.scrollPosition = Math.max(0, Math.min(scrollTop / maxScrollTop, 1));

				// 存储详细的滚动信息，用于精确的章节匹配
				this.scrollInfo = {
					scrollTop: scrollTop,
					scrollHeight: scrollHeight,
					clientHeight: clientHeight,
					maxScrollTop: maxScrollTop
				};

				// 记录滚动事件到阅读会话
				readingSessionManager.recordScrollEvent(this.scrollPosition, scrollTop);

				// 保存阅读进度
				this.saveReadingProgress();

				// 重置控制栏隐藏定时器
				if (this.isControlVisible) {
					this.showControls();
				}
			},

			saveReadingProgress() {
				// 计算基于章节的真实阅读进度
				let chapterBasedProgress = 0;
				if (this.chapters.length > 0) {
					const currentChapterIndex = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));
					if (currentChapterIndex !== -1) {
						// 基于章节的进度：当前章节位置 / 总章节数
						// 加上当前章节内的滚动进度作为微调
						const chapterProgress = currentChapterIndex / this.chapters.length;
						const inChapterProgress = this.scrollPosition / this.chapters.length;
						chapterBasedProgress = Math.max(0, Math.min(chapterProgress + inChapterProgress, 1));
					}
				}

				// 保存阅读进度
				const progress = {
					bookId: this.bookId,
					chapterId: this.chapterId,
					position: this.scrollPosition, // 保留原始滚动位置
					chapterBasedProgress: chapterBasedProgress, // 新增：基于章节的真实进度
					percentage: Math.floor(chapterBasedProgress * 100), // 新增：进度百分比
					timestamp: Date.now()
				};
				uni.setStorageSync(`readingProgress_${this.bookId}`, JSON.stringify(progress));

				// 同时保存到阅读历史
				this.saveToReadingHistory();
			},
			saveToReadingHistory() {
				// 获取现有的阅读历史
				let readingHistory = [];
				try {
					const historyData = uni.getStorageSync('readingHistory');
					if (historyData) {
						readingHistory = JSON.parse(historyData);
					}
				} catch (e) {
					console.error('读取阅读历史失败:', e);
				}

				// 计算基于章节的真实阅读进度
				let chapterBasedProgress = 0;
				if (this.chapters.length > 0) {
					const currentChapterIndex = this.chapters.findIndex(ch => String(ch.id) === String(this.chapterId));
					if (currentChapterIndex !== -1) {
						const chapterProgress = currentChapterIndex / this.chapters.length;
						const inChapterProgress = this.scrollPosition / this.chapters.length;
						chapterBasedProgress = Math.max(0, Math.min(chapterProgress + inChapterProgress, 1));
					}
				}

				// 创建历史记录项
				const historyItem = {
					bookId: this.bookId,
					bookTitle: this.currentChapterInfo.title.split(' ')[0] || '未知书籍', // 从章节标题提取书名
					chapterId: this.chapterId,
					chapterTitle: this.currentChapterInfo.title,
					readTime: new Date().toISOString(),
					progress: chapterBasedProgress, // 使用基于章节的真实进度
					percentage: Math.floor(chapterBasedProgress * 100) // 添加百分比
				};

				// 移除相同书籍的旧记录
				readingHistory = readingHistory.filter(item => item.bookId !== this.bookId);

				// 添加新记录到开头
				readingHistory.unshift(historyItem);

				// 限制历史记录数量（最多保存50条）
				if (readingHistory.length > 50) {
					readingHistory = readingHistory.slice(0, 50);
				}

				// 保存到本地存储
				try {
					uni.setStorageSync('readingHistory', JSON.stringify(readingHistory));
				} catch (e) {
					console.error('保存阅读历史失败:', e);
				}
			},
			toggleSettings() {
				this.isSettingsVisible = !this.isSettingsVisible;
				this.isChapterListVisible = false;

				// 重置控制栏隐藏定时器
				if (this.isSettingsVisible) {
					if (this.controlHideTimer) {
						clearTimeout(this.controlHideTimer);
						this.controlHideTimer = null;
					}
				} else {
					this.showControls();
				}
			},
			toggleChapterList() {
				this.isChapterListVisible = !this.isChapterListVisible;
				this.isSettingsVisible = false;

				// 重置控制栏隐藏定时器
				if (this.isChapterListVisible) {
					if (this.controlHideTimer) {
						clearTimeout(this.controlHideTimer);
						this.controlHideTimer = null;
					}
				} else {
					this.showControls();
				}
			},
			toggleMoreOptions() {
				this.isMoreOptionsVisible = !this.isMoreOptionsVisible;
			},
			toggleDayNight() {
				this.isDarkMode = !this.isDarkMode;
				this.currentTheme = this.isDarkMode ? 'dark' : 'default';
				this.saveReadingSettings();
			},
			changeFontSize(delta) {
				const oldValue = this.fontSize;
				this.fontSize = Math.max(12, Math.min(30, this.fontSize + delta));
				readingSessionManager.recordSettingChange('fontSize', oldValue, this.fontSize);
				this.saveReadingSettings();
			},
			changeLineHeight(delta) {
				const oldValue = this.lineHeight;
				this.lineHeight = Math.max(1.0, Math.min(3.0, parseFloat((this.lineHeight + delta).toFixed(1))));
				readingSessionManager.recordSettingChange('lineHeight', oldValue, this.lineHeight);
				this.saveReadingSettings();
			},
			changeLetterSpacing(delta) {
				const oldValue = this.letterSpacing;
				this.letterSpacing = Math.max(0, Math.min(5, parseFloat((this.letterSpacing + delta).toFixed(1))));
				readingSessionManager.recordSettingChange('letterSpacing', oldValue, this.letterSpacing);
				this.saveReadingSettings();
			},
			changeTheme(themeId) {
				const oldValue = this.currentTheme;
				this.currentTheme = themeId;
				this.isDarkMode = themeId === 'dark';
				readingSessionManager.recordSettingChange('theme', oldValue, themeId);
				this.saveReadingSettings();
			},
			changeFont(fontId) {
				const oldValue = this.currentFont;
				this.currentFont = fontId;
				readingSessionManager.recordSettingChange('font', oldValue, fontId);
				this.saveReadingSettings();
			},
			async selectChapter(chapter) {
				if (chapter.id === this.chapterId) {
					this.toggleChapterList();
					return;
				}

				console.log('📖 切换到章节:', chapter.title);

				// 记录章节切换
				const fromChapter = this.getCurrentChapterInfo();
				const toChapter = {
					id: chapter.id,
					title: chapter.title,
					index: this.chapters.findIndex(c => c.id === chapter.id) + 1
				};
				readingSessionManager.recordChapterSwitch(fromChapter, toChapter);

				// 切换到选中的章节
				this.chapterId = chapter.id;

				try {
					// 重新加载当前章节
					await this.loadCurrentChapter();

					// 重置滚动位置
					this.scrollTop = 0;
					this.scrollPosition = 0;

					// 更新边界状态
					const currentIndex = this.chapters.findIndex(c => c.id === chapter.id);
					this.hasMorePrev = currentIndex > 0;
					this.hasMoreNext = currentIndex < this.chapters.length - 1;

					// 开始预加载
					this.startPreloading();

					console.log('✅ 章节切换完成');
				} catch (error) {
					console.error('❌ 章节切换失败:', error);
					uni.showToast({
						title: '章节切换失败',
						icon: 'none'
					});
				}

				this.toggleChapterList();
			},

			navigateBack() {
				uni.navigateBack();
			},

			// 估算当前章节索引
			estimateCurrentChapterIndex() {
				if (this.chapterContents.length === 0) return 0;
				if (this.chapterContents.length === 1) return 0;

				// 使用精确的内容匹配方法
				const currentChapterId = this.getCurrentChapterByContent();
				if (currentChapterId) {
					// 在chapterContents中查找对应的索引
					const index = this.chapterContents.findIndex(chapter => String(chapter.id) === String(currentChapterId));
					return index >= 0 ? index : 0;
				}

				return 0;
			},

			// 基于可见内容精确匹配当前章节
			getCurrentChapterByContent() {
				if (!this.scrollInfo || this.allContent.length === 0) {
					console.log('📍 内容匹配: 缺少滚动信息或内容，回退到当前章节ID:', this.chapterId);
					return this.chapterId; // 回退到当前章节ID
				}

				// 计算当前可见区域的中心点对应的内容索引
				const { scrollTop, clientHeight, scrollHeight } = this.scrollInfo;
				const viewportCenter = scrollTop + clientHeight / 2;

				// 估算每个内容项的平均高度（基于总高度和内容项数量）
				const averageItemHeight = scrollHeight / this.allContent.length;

				// 计算可见区域中心点对应的内容项索引
				const centerContentIndex = Math.floor(viewportCenter / averageItemHeight);
				const safeIndex = Math.max(0, Math.min(centerContentIndex, this.allContent.length - 1));

				// 获取该内容项对应的章节索引
				const contentItem = this.allContent[safeIndex];

				console.log('📍 内容匹配详情:', {
					scrollTop,
					clientHeight,
					scrollHeight,
					viewportCenter,
					averageItemHeight,
					centerContentIndex,
					safeIndex,
					totalContentItems: this.allContent.length,
					contentItem: contentItem ? {
						type: contentItem.type,
						chapterIndex: contentItem.chapterIndex,
						content: contentItem.content?.substring(0, 50) + '...'
					} : null
				});

				if (contentItem && typeof contentItem.chapterIndex === 'number') {
					const chapterIndex = contentItem.chapterIndex;
					if (chapterIndex >= 0 && chapterIndex < this.chapterContents.length) {
						const matchedChapterId = this.chapterContents[chapterIndex].id;
						console.log('📍 匹配到章节:', {
							chapterIndex,
							chapterId: matchedChapterId,
							chapterTitle: this.chapterContents[chapterIndex].title
						});
						return matchedChapterId;
					}
				}

				// 如果无法确定，返回当前章节ID
				console.log('📍 内容匹配: 无法确定章节，回退到当前章节ID:', this.chapterId);
				return this.chapterId;
			},

			// 获取当前可见的章节
			getVisibleChapter() {
				// 基于滚动位置和章节内容来判断当前可见的章节
				if (this.chapterContents.length === 0) return null;

				// 如果只有一个章节，直接返回
				if (this.chapterContents.length === 1) {
					return this.chapterContents[0];
				}

				// 在多章节模式下，通过分析allContent来确定当前章节
				const allContentItems = this.allContent;
				if (allContentItems.length === 0) {
					return this.chapterContents[0];
				}

				// 根据滚动位置估算当前可见的内容项
				const scrollRatio = this.scrollPosition;
				const contentIndex = Math.floor(scrollRatio * allContentItems.length);
				const safeContentIndex = Math.max(0, Math.min(contentIndex, allContentItems.length - 1));

				// 获取该内容项对应的章节索引
				const currentContentItem = allContentItems[safeContentIndex];
				if (currentContentItem && typeof currentContentItem.chapterIndex === 'number') {
					const chapterIndex = currentContentItem.chapterIndex;
					if (chapterIndex >= 0 && chapterIndex < this.chapterContents.length) {
						return this.chapterContents[chapterIndex];
					}
				}

				// 回退方案：基于滚动比例估算
				const chapterIndex = Math.floor(scrollRatio * this.chapterContents.length);
				const safeIndex = Math.max(0, Math.min(chapterIndex, this.chapterContents.length - 1));
				return this.chapterContents[safeIndex];
			},

			// 更新当前章节ID（用于目录高亮）
			updateCurrentChapterId() {
				// 使用防抖机制，避免频繁更新
				if (this.chapterUpdateTimer) {
					clearTimeout(this.chapterUpdateTimer);
				}

				this.chapterUpdateTimer = setTimeout(() => {
					const visibleChapter = this.getVisibleChapter();
					if (visibleChapter && visibleChapter.id !== this.chapterId) {
						console.log('📖 当前可见章节变更:', visibleChapter.title);
						const oldChapterId = this.chapterId;
						this.chapterId = visibleChapter.id;

						// 更新阅读会话的章节信息
						const chapterInfo = {
							id: visibleChapter.id,
							title: visibleChapter.title,
							index: this.getCurrentChapterIndex()
						};

						// 记录章节切换到阅读会话
						if (readingSessionManager.getCurrentSession()) {
							const fromChapter = {
								id: oldChapterId,
								title: '上一章节',
								index: 0
							};
							readingSessionManager.recordChapterSwitch(fromChapter, chapterInfo);
						}
					}
				}, 500); // 500ms防抖延迟
			},


		}
	}
</script>

<style>
	/* 主题样式 */
	.theme-default {
		--bg-color: #FFFFFF;
		--text-color: #333333;
		--border-color: #EEEEEE;
		--control-bg: rgba(255, 255, 255, 0.9);
		--control-text: #333333;
		--panel-bg: #FFFFFF;
	}

	.theme-cream {
		--bg-color: #F8F3E8;
		--text-color: #5C4B51;
		--border-color: #E8DFD3;
		--control-bg: rgba(248, 243, 232, 0.9);
		--control-text: #5C4B51;
		--panel-bg: #F8F3E8;
	}

	.theme-dark {
		--bg-color: #222222;
		--text-color: #DDDDDD;
		--border-color: #444444;
		--control-bg: rgba(34, 34, 34, 0.9);
		--control-text: #DDDDDD;
		--panel-bg: #333333;
	}

	.theme-green {
		--bg-color: #E8F3E8;
		--text-color: #2E4052;
		--border-color: #D3E8D3;
		--control-bg: rgba(232, 243, 232, 0.9);
		--control-text: #2E4052;
		--panel-bg: #E8F3E8;
	}

	/* 字体样式 */
	.font-default {
		--font-family: sans-serif;
	}

	.font-serif {
		--font-family: serif;
	}

	.font-mono {
		--font-family: monospace;
	}

	/* 基础样式 */
	.reader-container {
		position: relative;
		height: 100vh;
		background-color: var(--bg-color);
		color: var(--text-color);
		font-family: var(--font-family);
	}

	/* 控制栏样式 */
	.top-bar, .bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		background-color: var(--control-bg);
		color: var(--control-text);
		z-index: 100;
		transition: transform 0.3s ease;
	}

	.top-bar {
		top: 0;
		min-height: 90rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		border-bottom: 1rpx solid var(--border-color);
		transform: translateY(-100%);
		box-sizing: border-box;
	}

	.top-bar.visible {
		transform: translateY(0);
	}

	.bottom-bar {
		bottom: 0;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid var(--border-color);
		transform: translateY(100%);
	}

	.bottom-bar.visible {
		transform: translateY(0);
	}

	.back-btn, .more-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.back-icon, .more-icon {
		font-size: 40rpx;
	}

	.chapter-title {
		flex: 1;
		text-align: center;
	}

	.title-text {
		font-size: 32rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.progress-info {
		text-align: center;
		margin-bottom: 20rpx;
	}

	.progress-text {
		font-size: 26rpx;
	}

	.bottom-controls {
		display: flex;
		justify-content: space-around;
	}

	.control-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.control-icon {
		font-size: 40rpx;
		margin-bottom: 10rpx;
	}

	.control-text {
		font-size: 24rpx;
	}

	/* 内容区域样式 */
	.content-scroll {
		height: 100%;
		box-sizing: border-box;
		padding: 30rpx;
	}

	/* 连续内容样式 */
	.continuous-content {
		min-height: 100%;
	}

	/* 加载指示器 */
	.loading-indicator {
		text-align: center;
		padding: 40rpx 0;
		color: #999;
	}

	.loading-text {
		font-size: 28rpx;
	}

	/* 结束指示器 */
	.end-indicator {
		text-align: center;
		padding: 60rpx 0;
		color: #666;
	}

	.end-text {
		font-size: 28rpx;
	}

	/* 章节分隔线 */
	.chapter-divider {
		margin: 60rpx 0;
		text-align: center;
	}

	.divider-line {
		height: 2rpx;
		background: linear-gradient(to right, transparent, var(--border-color), transparent);
		margin: 0 auto;
		width: 60%;
	}

	/* 章节标题区域 */
	.chapter-title-section {
		text-align: center;
		margin: 60rpx 0 40rpx 0;
	}

	.chapter-title-text {
		font-size: 40rpx;
		font-weight: bold;
		color: var(--text-color);
	}

	.content-paragraphs {
		margin-bottom: 40rpx;
	}

	.paragraph {
		margin-bottom: 30rpx;
		text-indent: 2em;
	}

	.paragraph-text {
		word-wrap: break-word;
	}

	.chapter-footer {
		padding: 40rpx 0;
		border-top: 1rpx solid var(--border-color);
	}

	.chapter-nav {
		display: flex;
		justify-content: space-between;
	}

	.nav-btn {
		padding: 20rpx 40rpx;
		background-color: var(--bg-color);
		border: 1rpx solid var(--border-color);
		border-radius: 10rpx;
	}

	.nav-text {
		font-size: 28rpx;
	}

	/* 设置面板样式 */
	.settings-panel, .chapter-list-panel {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: var(--panel-bg);
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		z-index: 200;
		transform: translateY(100%);
		transition: transform 0.3s ease;
		max-height: 70vh;
		display: flex;
		flex-direction: column;
		/* 确保面板有背景色，即使CSS变量失效 */
		background: var(--panel-bg, #FFFFFF);
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.settings-panel.visible, .chapter-list-panel.visible {
		transform: translateY(0);
	}

	.panel-header, .settings-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid var(--border-color);
	}

	.panel-title, .settings-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.close-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.close-icon {
		font-size: 40rpx;
	}

	.settings-content {
		padding: 20rpx 30rpx;
		overflow-y: auto;
	}

	.setting-group {
		margin-bottom: 30rpx;
	}

	.setting-label {
		font-size: 28rpx;
		margin-bottom: 20rpx;
		display: block;
	}

	.setting-controls {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.setting-value {
		font-size: 28rpx;
	}

	.theme-options, .font-options {
		display: flex;
		flex-wrap: wrap;
	}

	.theme-option, .font-option {
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		padding: 10rpx;
		border: 1rpx solid var(--border-color);
		border-radius: 10rpx;
	}

	.theme-option.active, .font-option.active {
		border-color: #1E5F74;
	}

	.theme-color {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		margin-bottom: 10rpx;
	}

	.theme-name, .font-name {
		font-size: 24rpx;
		text-align: center;
		display: block;
	}

	/* 章节列表样式 */
	.chapter-list-scroll {
		flex: 1;
		overflow-y: auto;
	}

	.chapter-list-item {
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid var(--border-color);
	}

	.chapter-list-item.active {
		background-color: rgba(30, 95, 116, 0.1);
	}

	.chapter-list-title {
		font-size: 28rpx;
	}
</style>
